<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\Sticker;
use App\Models\StickerPack;
use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class Update extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '版本升级脚本';

    /**
     * Execute the console command.
     *
     * @throws Exception
     */
    public function handle()
    {
        $lockFile = storage_path('update.lock');
        if (File::exists($lockFile)) {
            $this->components->error("请先删除{$lockFile}后再执行");
            return;
        }
        $this->runUpdates();
        File::put($lockFile, \Safe\date('Y-m-d H:i:s'));
        $this->components->info('更新成功');
    }

    protected function runUpdates(): void
    {
        $categories = Category::query()->get();
        /** @var Category $category */
        foreach ($categories as $category) {
            $pack = StickerPack::query()->create([
                'name'  => $category->name,
                'image' => (string)$category->icon,
            ]);
            $category->stickers()->update([
                'pack_id' => $pack->getKey(),
            ]);
        }
    }
}
