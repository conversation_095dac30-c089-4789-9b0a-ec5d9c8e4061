<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use OpenApi\Attributes as OA;

/**
 * 用户第三方登录账户
 *
 * @property int     $id
 * @property string  $user_id
 * @property int     $provider
 * @property ?string $identity
 * @property ?string $name
 * @property ?string $email
 * @property ?string $nickname
 * @property ?string $avatar
 * @property User    $user
 */
#[OA\Schema(properties: [
    new OA\Property(property: 'name', description: '客户端名称'),
    new OA\Property(property: 'logo', description: '客户端Logo'),
])]
class SocialiteUser extends Authenticatable
{
    use HasFactory, SoftDeletes;
    use HasUuids;

    protected $table    = 'socialite_users';
    protected $fillable = [
        'user_id',
        'provider',
        'app_id',
        'identity_key',
        'identity',
        'nickname',
        'name',
        'email',
        'avatar',
        'attributes',
    ];
    protected $casts    = [
        'attributes' => 'json',
    ];
    protected $hidden   = [
        'user_id',
        'identity',
        'email',
        'attributes',
        self::CREATED_AT,
        self::UPDATED_AT,
        Model::DELETED_AT,
    ];

    const PROVIDER = [
        'github'             => 1,
        'wechat'             => 2,
        'qq'                 => 3,
        'weibo'              => 4,
        'gitee'              => 5,
        'alipay'             => 6,
        'linuxdo'            => 7,
        'wechat_miniprogram' => 8,
    ];

    const IDENTITY_KEY_WECHAT_OPENID  = 'openid';
    const IDENTITY_KEY_WECHAT_UNIONID = 'unionid';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }
}
