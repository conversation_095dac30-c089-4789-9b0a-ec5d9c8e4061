<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WeChatAppUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'    => 'sometimes|required|max:20',
            'app_id'  => 'string|max:255',
            'secret'  => 'string|max:255',
            'token'   => 'string|max:255',
            'aes_key' => 'string|max:255',
        ];
    }
}
