<?php

namespace App\Console;

use App\Console\Commands\UpdateStickerOrder;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $outputFile = storage_path(sprintf('logs/schedule-%s.log', date('Y-m-d')));
        $schedule->command('auth:clear-resets')->everyFifteenMinutes()->appendOutputTo($outputFile);
        $schedule->command('passport:purge')->hourly()->appendOutputTo($outputFile);
        $schedule->command('update-views')->daily()->at('0:30')->appendOutputTo($outputFile);
        $schedule->command('model:prune')->daily();
//        $schedule->command(UpdateStickerOrder::class);
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
