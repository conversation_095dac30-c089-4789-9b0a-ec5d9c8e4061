<?php

namespace App\Http\Resources;

use App\Models\LoginLog;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin LoginLog
 */
class LoginLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $resource                    = parent::toArray($request);
        $resource['login_type_text'] = trans('models/loginLog.loginTypes.' . $this->login_type);

        return $resource;
    }
}
