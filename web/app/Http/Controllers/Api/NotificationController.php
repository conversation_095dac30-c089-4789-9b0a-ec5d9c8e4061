<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Utils\Http\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;

class NotificationController extends Controller
{
    /**
     * @throws ValidationException
     */
    public function read(Request $request)
    {
        $this->validate($request, ['id' => 'array']);
        /** @var User $user */
        $user = $request->user();
        $ids  = $request->input('id');
        try {
            $user->notifications()->whereNull('read_at')->whereIn('id', $ids)->update(['read_at' => now()]);
        } catch (\Exception $e) {
            Log::debug('阅读通知', ['error' => $e->getMessage(), 'input' => $ids]);
            throw new InvalidArgumentException('参数错误');
        }

        return Api::okay();
    }
}
