<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StickerStoreRequest;
use App\Http\Requests\StickerUpdateRequest;
use App\Models\Sticker;
use App\Utils\Http\Api;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Throwable;

class StickerController extends Controller
{
    public function index(Request $request)
    {
        $stickers = Sticker::with(['categories:id,name,icon', 'user:id,name'])
            ->when($request->query('category_id'), function($query, $categoryId) {
                $query->whereHas('categories', function($q) use ($categoryId) {
                    $q->where('categories.id', $categoryId);
                });
            })
            ->when($request->query('name'), fn($query, $name) =>
                $query->where('name', 'like', sprintf('%%%s%%', $name))
            )
            ->when($request->query('user_id'), fn($query, $userId) =>
                $query->where('user_id', $userId)
            )
            ->latest()
            ->paginate();

        return Api::okay($stickers);
    }

    /**
     * @throws Throwable
     */
    public function store(StickerStoreRequest $request)
    {
        $data = $request->validated();
        $sticker = DB::transaction(function () use ($data) {
            /** @var Sticker $sticker */
            $sticker = Sticker::create($data);
            if (isset($data['categories'])) {
                $sticker->categories()->attach($data['categories']);
            }
            return $sticker;
        });

        return Api::okay($sticker);
    }

    public function show($id)
    {
        return Api::okay(
            Sticker::with(['categories:id,name,icon', 'user:id,name'])
                ->findOrFail($id)
        );
    }

    /**
     * @throws Throwable
     */
    public function update($id, StickerUpdateRequest $request)
    {
        $sticker = Sticker::findOrFail($id);
        $data = $request->validated();

        DB::transaction(function () use ($sticker, $data) {
            $sticker->update($data);

            if (isset($data['categories'])) {
                $sticker->categories()->sync($data['categories']);
            }
        });

        return Api::okay($sticker);
    }

    public function destroy($id)
    {
        $sticker = Sticker::findOrFail($id);
        $sticker->delete();

        return Api::okay();
    }
}
