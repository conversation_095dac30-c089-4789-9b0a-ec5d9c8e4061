<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\ChangeUserStatus;
use App\Admin\Columns\Date;
use App\Admin\Repositories\User;
use App\Models\Sticker;
use App\Utils\Eloquent\Concern\HasFavoritedBy;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;

class UserController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new User(['extends']), function (Grid $grid) {
            $grid->model()->orderByDesc('created_at');
            $grid->column('avatar')->image(width: 50, height: 50);
            $grid->column('nickname');
            $grid->column('gender')->using(trans('models/user.gender'));
            $grid->column('email');
            $grid->column('disabled_until')->sortable();
            $grid->column('created_at')->displayUsing(Date::class)->sortable();
            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete(); //  禁用删除
                $actions->append(new ChangeUserStatus);
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->disableCreateButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('nickname');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new User(['favorites.model', 'extends']), function (Show $show) {
            $show->field('uuid');
            $show->field('name');
            $show->field('nickname');
            $show->field('gender');
            $show->field('email');
            $show->field('email_verified_at');
            $show->field('avatar')->image();
            $show->field('password');
            $show->field('disabled_until');
            $show->field('remember_token');
            $show->field('created_at');
            $show->field('updated_at');

            $show->relation('favorites', function (\App\Models\User $model) {
                $grid = new Grid(new \App\Admin\Repositories\Favorite());
                $grid->model()->where('user_id', $model->getKey());
                $grid->column('model')
                    ->display(function ($model, Grid\Column $column) {
                        /** @var HasFavoritedBy $model */
                        if ($model instanceof Sticker) {
                            return <<<HTML
<img data-action="preview-img" src="{$model->image}" style="max-width:200px;max-height:200px" class="img img-thumbnail">
HTML;
                        }
                    });
                $grid->id();
                $grid->created_at();
                $grid->updated_at();

                return $grid;
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new User(), function (Form $form) {
            $form->text('name');
            $form->text('nickname');
            $form->radio('gender')->options(trans('models/user.gender'));
            $form->email('email')->readOnly();
            $form->datetime('email_verified_at');
            $form->image('avatar');
            $form->password('password');
            $form->datetime('disabled_until');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
