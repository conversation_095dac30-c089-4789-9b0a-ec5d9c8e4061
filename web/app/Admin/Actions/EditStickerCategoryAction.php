<?php

namespace App\Admin\Actions;

use Dcat\Admin\Actions\Action;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class EditStickerCategoryAction extends RowAction
{
    public $name = '编辑分类';

    /**
     * @param Model   $model
     * @param Request $request
     *
     * @return \Dcat\Admin\Actions\Response
     */
    public function handle(Model $model, Request $request)
    {
        // 默认处理逻辑：保存分类
        // 获取选中的分类ID
        $categoryIds = $request->get('categories', []);

        // 同步贴纸的分类关系
        $model->categories()->sync($categoryIds);

        return $this->response()
            ->success('分类更新成功')
            ->refresh();
    }

    /**
     * 渲染模态框内容
     *
     * @return Modal
     */
    public function render()
    {
        // 当前贴纸的分类IDs
        $stickerId          = $this->getKey();
        $currentCategoryIds = $this->row->categories()->pluck('id')->toJson();
        // 模态框内容
        $modalContent = <<<HTML
<div class="edit-category-modal" id="edit-category-modal-{$stickerId}">
    <form id="edit-category-form-{$stickerId}">
        <div class="form-group">
            <label>选择分类</label>
            <div class="category-checkboxes" id="category-checkboxes-{$stickerId}">
                <p class="text-muted">加载中...</p>
            </div>
        </div>
        <button type="submit" class="btn btn-primary">保存</button>
    </form>
</div>
HTML;

        // 创建模态框
        $modal = Modal::make()
            ->title('编辑分类')
            ->body($modalContent)
            ->button('<button class="btn btn-sm btn-outline-primary">' . $this->name . '</button>');

        // 添加脚本处理表单提交和分类数据加载
        $script = <<<JS
// 表单提交处理
$('#edit-category-form-{$stickerId}').on('submit', function(e) {
    e.preventDefault();

    var selectedCategories = [];
    $('#edit-category-form-{$stickerId} input[name="categories[]"]:checked').each(function() {
        selectedCategories.push($(this).val());
    });

    Dcat.NP.start();

    $.ajax({
        url: '{$this->handlerRoute()}',
        type: 'POST',
        data: {
            _key: '{$stickerId}',
            _action: '{$this->makeCalledClass()}',
            categories: selectedCategories
        },
        success: function (data) {
            Dcat.NP.done();
            if (data.status) {
                Dcat.success(data.message);
                $('#{$modal->id()}').modal('hide');
                Dcat.reload();
            } else {
                Dcat.error(data.message || '保存失败');
            }
        },
        error: function (xhr, textStatus, errorThrown) {
            Dcat.NP.done();
            Dcat.handleAjaxError(xhr, textStatus, errorThrown);
        }
    });
});

// 模态框显示时加载分类数据
$('#{$modal->id()}').on('shown.bs.modal', function () {
    loadCategories{$stickerId}();
});

// 加载分类数据
function loadCategories{$stickerId}() {
    // 如果已经加载过分类数据，则不再重复加载
    if ($('#category-checkboxes-{$stickerId}').data('loaded')) {
        return;
    }

    // 并行加载分类数据和贴纸当前分类数据
    $.when(
        $.ajax({
            url: '/admin/api/categories',
            type: 'GET'
        }),
    ).done(function(categoriesResponse) {
        renderCategories{$stickerId}(categoriesResponse, $currentCategoryIds);
        $('#category-checkboxes-{$stickerId}').data('loaded', true);
    }).fail(function(xhr, textStatus, errorThrown) {
        Dcat.handleAjaxError(xhr, textStatus, errorThrown);
    });
}

// 渲染分类复选框
function renderCategories{$stickerId}(categories, currentCategoryIds) {
    var html = '';

    categories.forEach(function(category) {
        var checked = currentCategoryIds.includes(category.id) ? 'checked' : '';
        html += "<div class='form-check'>" +
                "<input class='form-check-input' type='checkbox' name='categories[]' value='"+category.id+"' id='category-"+category.id+"-{$stickerId}' "+checked+">" +
                "<label class='form-check-label' for='category-"+category.id+"-{$stickerId}'>"+category.text+"</label>" +
                "</div>";

        // 子分类
        if (category.children && category.children.length > 0) {
            html += "<div class='ml-4'>";
            category.children.forEach(function(child) {
                var childChecked = currentCategoryIds.includes(child.id) ? 'checked' : '';
                html += "<div class='form-check'>" +
                        "<input class='form-check-input' type='checkbox' name='categories[]' value='"+child.id+"' id='category-"+child.id+"-{$stickerId}' "+childChecked+">" +
                        "<label class='form-check-label' for='category-"+child.id+"-{$stickerId}'>"+category.text+" -> "+child.text+"</label>" +
                        "</div>";
            });
            html += "</div>";
        }
    });

    $('#category-checkboxes-{$stickerId}').html(html);
}
JS;

        Admin::script($script);

        return $modal->render();
    }
}
