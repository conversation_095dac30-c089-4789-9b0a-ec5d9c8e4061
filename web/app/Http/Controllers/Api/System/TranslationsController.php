<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\TranslationStoreRequest;
use App\Http\Requests\TranslationUpdateRequest;
use App\Models\Translation;
use App\Utils\Http\Api;
use Illuminate\Http\Request;
use InvalidArgumentException;

class TranslationsController extends Controller
{
    public function index(Request $request)
    {
        return Api::okay(
            Translation::query()
                ->when($request->get('keyword'), function ($query, $keyword) {
                    $query->where('zh-CN', 'like', "%$keyword%")
                        ->orWhere('en', 'like', "%$keyword%");
                })
                ->latest('id')
                ->paginate(page_size_resolver())
        );
    }

    public function store(TranslationStoreRequest $request)
    {
        return Api::okay(Translation::query()->create($request->validated()));
    }

    public function update(Translation $translation, TranslationUpdateRequest $request)
    {
        $key = $request->validated('key');
        if ($translation->key !== $key && Translation::query()->where('key', $key)->exists()) {
            throw new InvalidArgumentException('key 已存在');
        }

        $translation->update($request->validated());

        return Api::okay();
    }

    public function destroy(Translation $translation)
    {
        $translation->delete();

        return Api::okay();
    }

    public function refresh()
    {
        return Api::okay();
    }
}
