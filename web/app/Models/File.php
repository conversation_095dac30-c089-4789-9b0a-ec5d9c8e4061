<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

/**
 * @property bool       $is_dir
 * @property string     $path
 * @property string     $name
 * @property string     $parent_id
 * @property string     $url
 * @property string     $uuid
 * @property Collection $subFiles
 */
class File extends Model
{
    use SoftDeletes;
    use HasFactory, HasUuids;

    protected $fillable = ['parent_id', 'path', 'size', 'mime_type', 'hash', 'name'];
    protected $appends  = ['url'];
    protected $hidden   = ['id', self::CREATED_AT, self::UPDATED_AT];
    protected $casts    = ['is_dir' => 'boolean'];

    const TYPE = [
        'picture'  => 1,
        'video'    => 2,
        'document' => 3,
    ];

    public function getUrlAttribute()
    {
        return $this->is_dir ? "" : url(Storage::url($this->path));
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }

    public function subFiles(): HasMany
    {
        return $this->hasMany(File::class, 'parent_id', 'uuid');
    }
}
