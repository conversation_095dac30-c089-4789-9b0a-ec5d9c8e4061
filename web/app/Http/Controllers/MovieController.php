<?php

namespace App\Http\Controllers;

use App\Models\Movie;
use App\Utils\Http\Api;
use Illuminate\Http\Request;

class MovieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $m = Movie::query()->find('684cdbad88defa45e3084344');
        $m->pull('user', ['test2', 'test4']);
        return $m;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $v = Movie::query()->create([
            'type'   => 'movie',
            'values' => [
                'user' => [
                    'id' => 1,
                ]
            ]
        ]);

        return Api::okay($v);
    }

    /**
     * Display the specified resource.
     */
    public function show(Movie $movie)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Movie $movie)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Movie $movie)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Movie $movie)
    {
        //
    }
}
