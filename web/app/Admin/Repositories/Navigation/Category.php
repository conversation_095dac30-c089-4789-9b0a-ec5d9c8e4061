<?php

namespace App\Admin\Repositories\Navigation;

use App\Models\Navigation\SiteCategory as Model;
use Dcat\Admin\Repositories\EloquentRepository;
use App\Models\Navigation\Site as SiteModel;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class Category extends EloquentRepository
{
    protected $eloquentClass = Model::class;

    public static $categoryAllTree     = 'navigation_category_all_tree';
    public static $categoryWithSiteKey = 'navigation_category_with_site';

    public static function getCategoryWithSite()
    {
        return Cache::remember(self::$categoryWithSiteKey, 60 * 60 * 24, function () {
            $parents  = Model::with(['children' => fn($query) => $query->with('site')->where('status', 1)])
                ->where('parent_id', 0)
                ->where('status', 1)
                ->orderBy('order')
                ->get()
                ->keyBy('id');
            $children = $parents->pluck('children')->flatten(1);
            $totals   = SiteModel::query()
                ->whereIn('category_id', $children->pluck('id'))
                ->groupBy('category_id')
                ->select(DB::raw('category_id, count(id) as count'));
            $parents->transform(function (Model $category) use ($totals) {
                $category['site_num'] = (int)$totals->whereIn('category_id', $category->children->pluck('id'))->count();
                return $category;
            });

            return $parents;
        });
    }

    public static function getParentCategory()
    {
        $parentCategory = Model::where('parent_id', 0)->where('status', 1)->select('id', 'title as text')->orderBy('order', 'desc')->get();
        $data           = [];
        foreach ($parentCategory as $pac) {
            $data[$pac->id] = $pac->text;
        }
        return $data;
    }

    public static function getCategoryAllTree()
    {
        if (!Cache::has(self::$categoryAllTree)) {
            $parentCategory = Model::where('parent_id', 0)->where('status', 1)->select('id', 'title', 'description', 'icon')->orderBy('order', 'desc')->get();
            $res            = $parentCategory->toArray();
            foreach ($res as &$pac) {
                $pac['children'] = Model::where('parent_id', $pac['id'])->where('status', 1)->select('id', 'title', 'description', 'icon')->orderBy('order', 'desc')->get()->toArray();
            }
            // 保存24小时
            Cache::put(self::$categoryAllTree, $res, 24 * 60 * 60); // 24小时
        } else {
            $res = Cache::get(self::$categoryAllTree);
        }
        return $res;
    }
}
