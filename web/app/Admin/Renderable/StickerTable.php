<?php

namespace App\Admin\Renderable;

use App\Admin\Repositories\Sticker;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;

class StickerTable extends LazyRenderable
{
    public function grid(): Grid
    {
        $categoryId = request('key');
        return Grid::make(new Sticker(), function (Grid $grid) use ($categoryId) {
            $grid->model()->whereHas('categories', fn($query) => $query->where('id', $categoryId));
            $grid->view('admin.grid.image');
            $grid->column('id', __('ID'));
            $grid->column('name')->editable();
            $grid->column('image')->image(width: 100, height: 100);
        });
    }
}
