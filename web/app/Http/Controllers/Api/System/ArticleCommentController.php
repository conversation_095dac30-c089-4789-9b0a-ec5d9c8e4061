<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\ArticleCommentStoreRequest;
use App\Models\Article;
use App\Models\User;
use App\Utils\Http\Api;
use Illuminate\Http\Request;

class ArticleCommentController extends Controller
{
    public function index(Article $article)
    {
        return Api::okay($article->comments);
    }

    public function store($articleId, ArticleCommentStoreRequest $request)
    {
        /** @var Article $article */
        $article           = Article::query()->where('uuid', $articleId)->firstOrFail();
        $user              = $request->user();
        $inputs            = $request->validated();
        $inputs['user_id'] = $user->getKey();
        $article->comments()->create($inputs);

        return Api::okay();
    }

    public function destroy($articleId, $commentId, Request $request)
    {
        /**
         * @var User    $user
         * @var Article $article
         */
        $user    = $request->user();
        $article = $user->articles()->where('uuid', $articleId)->firstOrFail();
        $article->comments()->where('uuid', $commentId)->delete();

        return Api::okay();
    }
}
