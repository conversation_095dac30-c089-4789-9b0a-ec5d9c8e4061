<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class JWTAuthenticate
{
    public function handle(Request $request, \Closure $next)
    {
        if ($token = $request->get('token')) {
            $request->headers->set('Authorization', 'Bearer ' . $token);
            if ($user = \auth('api')->user()) {
                Auth::login($user);
            }

            return redirect($request->fullUrlWithoutQuery('token'));
        }

        return $next($request);
    }
}
