<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\RegistrationFieldStoreRequest;
use App\Http\Requests\RegistrationFormStoreRequest;
use App\Http\Requests\RegistrationFormUpdateRequest;
use App\Http\Requests\ActivityStoreRequest;
use App\Http\Requests\ActivityUpdateRequest;
use App\Http\Resources\EventCollection;
use App\Http\Resources\MessageCollection;
use App\Models\Activity;
use App\Models\EventRegistration;
use App\Models\EventRegistrationDefaultField;
use App\Models\EventRegistrationValue;
use App\Utils\Http\Api;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class EventController extends Controller
{
    public function index(Request $request)
    {
        $events = $request->user()
            ->activities()
            ->when($request->query('state'), function ($query, $state) {
                $query->where('state', $state);
            })
            ->when($request->query('keyword'), function ($query, $keyword) {
                $query->where('title', 'like', "%{$keyword}%");
            })
            ->latest()
            ->paginate();
        return Api::okay(new EventCollection($events));
    }

    public function store(ActivityStoreRequest $request)
    {
        /** @var Activity $event */
        $event = $request->user()->activities()->create($request->validated());
        $event->registrationForms()->create(['name' => '活动报名']);
        return Api::okay($event);
    }

    public function show($id)
    {
        return Api::okay(Activity::with(['user'])->findOrFail($id));
    }

    public function registrationForms(Activity $event)
    {
        return Api::okay($event->registrationForms()->withCount(['registrations'])->get());
    }

    public function update($id, ActivityUpdateRequest $request)
    {
        $input = $request->validated();
        $event  = $request->user()->activities()->findOrFail($id);
        $event->update($input);

        return Api::okay();
    }

    /**
     * @throws Exception
     */
    public function destroy($id, Request $request)
    {
        $event = $request->user()->activities()->findOrFail($id);
        if ($event->isState(Activity::STATE['living'])) {
            throw new Exception('正在直播中，请先结束直播');
        }

        $event->delete();

        return Api::okay();
    }

    public function start($eventId, Request $request)
    {
        $event = $request->user()->activities()->findOrFail($eventId);
        if ($event->isState(Activity::STATE['living'])) {
            throw new Exception('房间已经开始');
        }
        $updates = [
            'state'           => Activity::STATE['living'],
            'real_start_time' => Carbon::now(),
            'end_time'        => null,
        ];
        if (empty($event->stream_push_token)) {
            $updates['stream_push_token'] = Str::random(40);
        }
        $event->update($updates);

        return Api::okay();
    }

    public function end($eventId, Request $request)
    {
        $event = $request->user()->activities()->findOrFail($eventId);
        if (!$event->isState(Activity::STATE['living'])) {
            throw new Exception('房间未开始直播，不能结束');
        }
        $event->update([
            'state'    => Activity::STATE['closed'],
            'end_time' => Carbon::now(),
        ]);

        return Api::okay();
    }

    public function registrations(Activity $event, $formId)
    {
        $form          = $event->registrationForms()->findOrFail($formId);
        $registrations = $form->registrations()->with(['values', 'user'])->paginate();
        $registrations->getCollection()->transform(function (EventRegistration $registration) {
            $values = [];
            $registration->values->each(function (EventRegistrationValue $value) use (&$values) {
                $values[$value->name] = $value->value;
            });
            $registration           = $registration->only(['id', 'user', 'created_at']);
            $registration['values'] = $values;

            return $registration;
        });

        return Api::okay($registrations);
    }

    /**
     * @throws Exception
     */
    public function messages($eventId): JsonResponse
    {
        /** @var Activity $event */
        $event = Activity::query()->findOrFail($eventId);
        if (!$event->isState(Activity::STATE['living'])) {
            throw new Exception('房间未开启');
        }

        return Api::okay(new MessageCollection($event->messages()->with('user')->latest()->paginate()));
    }

    public function registrationFields(): JsonResponse
    {
        return Api::okay(EventRegistrationDefaultField::with(['children'])->where('parent_id', 0)->get());
    }

    public function getRegistrationForm(Activity $event)
    {
        return Api::okay($event->registrationForms()->first());
    }

    public function showRegistrationForm(Activity $event, $formId)
    {
        return Api::okay($event->registrationForms()->with(['fields'])->findOrFail($formId));
    }

    public function createRegistrationForm(Activity $event, RegistrationFormStoreRequest $request)
    {
        dd(1);
        $form = $event->registrationForms()->create($request->validated());

        return Api::okay($form->load(['fields']));
    }

    public function deleteRegistrationForm(Activity $event, $formId)
    {
        $event->registrationForms()->delete($formId);

        return Api::okay();
    }

    public function updateRegistrationForm(Activity $event, $formId, RegistrationFormUpdateRequest $request)
    {
        $form = $event->registrationForms()->findOrFail($formId);
        $form->update($request->validated());

        return Api::okay($form->load(['fields']));
    }

    public function fields(Activity $event)
    {
        return Api::okay($event->registrationForm->fields);
    }

    /**
     * @throws Exception
     */
    public function addFields(Activity $event, $formId, RegistrationFieldStoreRequest $request)
    {
        $form   = $event->registrationForms()->findOrFail($formId);
        $inputs = $request->safe(['name']);
        if ($form->fields()->where($inputs)->exists()) {
            throw new Exception('字段已存在');
        }

        $field = $form->fields()->create($request->safe()->all());

        return Api::okay($field);
    }

    public function deleteField(Activity $event, $formId, $fieldId)
    {
        $event->registrationForms()->findOrFail($formId)->fields()->where('id', $fieldId)->delete();

        return Api::okay();
    }

    public function updateField(Activity $event, $formId, $fieldId, Request $request)
    {
        $field = $event->registrationForms()->findOrFail($formId)->fields()->findOrFail($fieldId);
        $field->update($request->all());

        return Api::okay();
    }
}
