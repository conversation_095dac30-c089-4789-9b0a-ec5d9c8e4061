<?php

namespace App\Services;

use App\Models\Event;
use App\Models\User;
use App\Models\UserEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class UserService
{
    public function triggerEvent(
        User                           $user,
        string                         $slug,
        string                         $resource = '',
        string                         $description = '',
        float                          $points = 0,
        string|\DateTimeInterface|null $triggeredAt = null,
        ?array                         $details = null,
        string                         $scene = ''
    )
    {
        $event = Event::query()
            ->whereRaw("(`flag` & ?) = 0", [Event::FLAG['disabled']])
            ->where('slug', $slug)
            ->first();
        if (!$event) {
            throw new InvalidArgumentException('事件被禁用或者不存在');
        }

        $points = (($event->flag & Event::FLAG['customizable']) !== 0) && $points ? $points : $event->points;

        if ($event->points_rules) {
            foreach ($event->points_rules as $rule => $limit) {
                if (!$limit) {
                    continue;
                }

                switch ($rule) {
                    case Event::POINTS_RULES['countPerScene']:
                        if ($user->events()
                                ->where('event_id', $event->getKey())
                                ->where('scene', $scene)
                                ->where('points', '>', 0)
                                ->count() >= $limit) {
                            $points = 0;
                            goto SAVE;
                        }
                        break;
                    case Event::POINTS_RULES['countPerDay']:
                        if ($user->events()
                                ->where('event_id', $event->getKey())
                                ->where('resource', $resource)
                                ->where('points', '>', 0)
                                ->whereDate(UserEvent::CREATED_AT, Carbon::now()->toDateString())
                                ->count() >= $limit
                        ) {
                            $points = 0;
                            goto SAVE;
                        }
                        break;
                    case Event::POINTS_RULES['countPerWeek']:
                        if ($user->events()
                                ->where('event_id', $event->getKey())
                                ->where('resource', $resource)
                                ->where('points', '>', 0)
                                ->whereDate(UserEvent::CREATED_AT, '>=', Carbon::now()->startOfWeek()->toDateString())
                                ->whereDate(UserEvent::CREATED_AT, '<=', Carbon::now()->endOfWeek()->toDateString())
                                ->count() >= $limit
                        ) {
                            $points = 0;
                            goto SAVE;
                        }
                        break;
                    default:
                        throw new InvalidArgumentException("不支持的积分规则：$rule");
                }
            }
        }

        SAVE:
        // 如果不记录零积分事件
        if (($event->flag & Event::FLAG['recordZeroCreditEvent']) === 0 && $points === 0) {
            return null;
        }

        Log::info('积分记录', [
            'user_id'      => $user->getKey(),
            'slug'         => $slug,
            'resource'     => $resource,
            'description'  => $description,
            'points'       => $points,
            'triggered_at' => $triggeredAt,
            'details'      => $details,
            'scene'        => $scene,
        ]);
        return $user->events()->forceCreate([
            'event_id'            => $event->getKey(),
            'points'              => $points,
            'description'         => $description,
            'resource'            => $resource,
            'scene'               => $scene,
            'details'             => $details ?: null,
            UserEvent::CREATED_AT => $triggeredAt ?: now(),
        ]);
    }
}
