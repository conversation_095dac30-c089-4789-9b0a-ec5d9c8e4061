<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\UserService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UserEventDispatcher implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected User                           $user,
        protected string                         $slug,
        protected string                         $resource = '',
        protected string                         $description = '',
        protected float                          $points = 0,
        protected string|\DateTimeInterface|null $triggeredAt = null,
        protected ?array                         $details = null,
        protected string                         $scene = ''
    )
    {
    }

    /**
     * Execute the job.
     */
    public function handle(UserService $userService): void
    {
        try {
            $userService->triggerEvent(
                $this->user,
                $this->slug,
                $this->resource,
                $this->description,
                $this->points,
                $this->triggeredAt,
                $this->details,
                $this->scene
            );
        } catch (Exception $e) {
            Log::info('积分记录失败：' . $e->getMessage(), [
                'user_id'      => $this->user->getKey(),
                'slug'         => $this->slug,
                'resource'     => $this->resource,
                'description'  => $this->description,
                'points'       => $this->points,
                'triggered_at' => $this->triggeredAt,
                'details'      => $this->details,
                'scene'        => $this->scene,
            ]);
            $this->delete();
        }
    }
}
