<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $key
 */
class Translation extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table    = 'translations';
    protected $fillable = ['key', 'zh-CN', 'en'];
    protected $hidden   = [self::CREATED_AT, self::UPDATED_AT, self::DELETED_AT];
}
