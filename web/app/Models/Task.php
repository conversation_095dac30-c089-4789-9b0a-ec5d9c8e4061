<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property string $id
 * @property int    $state
 * @property array  $payload
 */
class Task extends Model
{
    use HasFactory, HasUuids;

    protected $table    = 'tasks';
    protected $fillable = ['id', 'state', 'error', 'payload', 'result'];
    protected $casts    = ['payload' => 'json', 'result' => 'json'];
    protected $hidden   = ['payload'];

    const STATE = [
        'ready'   => 0,
        'running' => 1,
        'failed'  => 2,
        'succeed' => 3,
    ];

    public function isRunning(): bool
    {
        return $this->state === self::STATE['running'];
    }
}
