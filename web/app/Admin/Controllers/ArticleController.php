<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Restore\BatchRestore;
use App\Admin\Actions\Restore\Restore;
use App\Admin\Columns\AvatarWithName;
use App\Admin\Columns\Date;
use App\Admin\Repositories\Article;
use App\Models\Article as ArticleModel;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Validation\Rule;

class ArticleController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Article::with(['author']), function (Grid $grid) {
            $grid->model()->orderByDesc('created_at');
            $grid->column('cover')->image();
            $grid->column('title');
            $grid->column('order')->editable();
            $grid->number('views');
            $grid->column('featured')->switch();
            $grid->column('draft')->switch();
            $grid->column('created_at')->displayUsing(Date::class)->sortable();
            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });

            $grid->quickSearch(['title']);
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('title', '标题');
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            if ($isTrashed = request('_scope_') == 'trashed') {
                $grid->disableCreateButton();
            }

            $grid->actions(function (Grid\Displayers\Actions $actions) use ($isTrashed) {
                if ($isTrashed) {
                    $actions->append(new Restore(ArticleModel::class));
                }
            });
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) use ($isTrashed) {
                if ($isTrashed) {
                    $batch->add(new BatchRestore(ArticleModel::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Article(), function (Show $show) {
            $show->field('id');
            $show->field('uuid');
            $show->field('user_id');
            $show->field('title');
            $show->field('abstract');
            $show->field('cover')->image();
            $show->field('content')->html(markdown2html($show->model()->content));
            $show->field('order');
            $show->field('views');
            $show->field('featured');
            $show->field('draft');
            $show->field('flag');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(Article::with(['author']), function (Form $form) {
            $authorSelector = $form->select('user_id', 'Author')
                ->options(User::class, 'id', 'nickname')
                ->ajax('api/users');
            if ($form->isEditing()) {
                $authorSelector->value($form->model()->user_id);
            }

            $form->ignore(['image_type']);
            $form->text('title')->maxLength(200)->rules([
                Rule::unique('articles', 'title')->ignore($form->model()->title),
            ])->required();
            $form->textarea('abstract');
            $form->radio('image_type')
                ->value(1)
                ->when(1, function (Form $form) {
                    $form->image('cover')
                        ->accept('jpg,png,gif,jpeg', 'image/*')
                        ->help('目前上传格式支持：jpg,png,gif,jpeg，最大为' . (getCiSize() / 1024) . 'M')
                        ->maxSize(getCiSize());
                })
                ->when(2, function (Form $form) {
                    $form->text('cover');
                })
                ->options([1 => '图片上传', 2 => '图片链接']);
            $form->easymde('content')->required();
            $form->number('order');
            $form->switch('featured');
            $form->switch('draft');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
