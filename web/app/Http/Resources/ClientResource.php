<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // $this->resource->makeHidden(['secret']);
        $item = parent::toArray($request);
        if (array_key_exists('logo', $item)) {
            $item['logo'] = full_url($item['logo']);
        }
        return $item;
    }
}
