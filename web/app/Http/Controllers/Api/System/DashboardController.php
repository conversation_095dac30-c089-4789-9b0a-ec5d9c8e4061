<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Utils\Http\Api;

class DashboardController extends Controller
{
    public function index()
    {
        return Api::okay([
            'users_count' => User::query()->count(),
            'new_users_count_today' => User::query()->whereDate('created_at', today())->count(),
            'articles_count' => $this->user->articles()->count(),
            'events_count' => $this->user->activities()->count(),
        ]);
    }
}
