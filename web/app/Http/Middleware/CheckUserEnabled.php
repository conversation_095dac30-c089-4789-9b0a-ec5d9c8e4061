<?php

namespace App\Http\Middleware;

use App\Models\User;
use Illuminate\Http\Request;

class CheckUserEnabled
{
    public function handle(Request $request, \Closure $next)
    {
        /** @var User $user */
        $user = $request->user();
        if ($user && $user->isDisabled()) {
            abort(403, "用户被封禁，解禁日期为{$user->disabled_until->toDateTimeString()}");
        }

        return $next($request);
    }
}
