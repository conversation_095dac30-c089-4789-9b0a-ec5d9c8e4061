<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use GuzzleHttp\Utils;

class DashscopeService
{
    const BASE_URI = 'https://dashscope.aliyuncs.com';

    public function chat($messages)
    {
        $apiKey = config('services.aliyun.dashscope.api_key');
        $client = new Client([
            'base_uri' => self::BASE_URI,
            'timeout' => 120,
        ]);
        $response = $client->post('/api/v1/services/aigc/text-generation/generation', [
            'headers' => [
//                'Accept'          => 'application/json',
                'Authorization' => 'Bearer ' . $apiKey,
                'X-DashScope-SSE' => 'enable',
            ],
            'json' => [
                'model' => 'qwen-max',
                'input' => [
                    'messages' => $messages,
                ],
            ],
            RequestOptions::STREAM => true,
        ]);
        $stream = $response->getBody();
        while (!$stream->eof()) {
            $i = $stream->read(1024);
            dump($i);
            if (connection_aborted()) {
                break;
            }
        }
        $data = json_decode($response->getBody()->getContents(), true);

        return $data['output']['text'] ?? '';
    }
}
