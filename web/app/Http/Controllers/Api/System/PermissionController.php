<?php

namespace App\Http\Controllers\Api\System;

use App\Utils\Http\Api;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Spatie\Permission\Models\Permission;

class PermissionController extends Controller
{
    public function index()
    {
        return Api::okay(Permission::query()->paginate());
    }

    public function store(Request $request)
    {
        $input = $this->validate($request, [
            'name' => 'required',
        ]);

        return Api::okay(Permission::query()->create($input));
    }
}
