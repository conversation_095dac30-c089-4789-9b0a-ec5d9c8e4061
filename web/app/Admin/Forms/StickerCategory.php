<?php

namespace App\Admin\Forms;

use App\Admin\Repositories\Category;
use App\Models\Sticker;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class StickerCategory extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        return $this->success('保存成功');
    }

    public function form()
    {
        $this->select('categories')
            ->options(app(Category::class)->getCategories(false))
            ->value(Sticker::query()->find(request('key'))->categories()->pluck('id')->first())
            ->required();
    }
}
