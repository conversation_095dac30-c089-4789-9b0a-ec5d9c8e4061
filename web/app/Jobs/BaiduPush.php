<?php

namespace App\Jobs;

use App\Models\Article;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class BaiduPush implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected Article $model
    )
    {
    }

    /**
     * Execute the job.
     *
     * @throws GuzzleException
     */
    public function handle(): void
    {
        $routeName = strtolower(class_basename($this->model));
        $token     = config('services.baidu.web_master.token');
        $url       = route($routeName . '.show', [$routeName => $this->model->getKey()]);
        $client    = new Client(['base_uri' => 'http://data.zz.baidu.com']);
        $response  = $client->post('/urls', [
            'headers' => [
                'Content-Type' => 'text/plain',
            ],
            'query'   => [
                'site'  => config('app.url'),
                'token' => $token,
            ],
            'body'    => implode("\n", [$url]),
        ]);

        Log::info('百度推送', [
            'url'      => $url,
            'response' => $response->getBody()->getContents(),
        ]);
    }
}
