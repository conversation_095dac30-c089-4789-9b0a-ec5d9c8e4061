<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StickerStoreRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'image'        => 'required|string|max:255',
            'name'         => 'string|max:255',
            'categories'   => 'sometimes|array',
            'categories.*' => 'exists:categories,id',
        ];
    }
}
