<?php

namespace App\Admin\Actions\Grid;

use App\Models\User;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

/**
 * @property User $row
 */
class ChangeUserStatus extends RowAction
{
    /**
     * @return string
     */
    protected $title = '禁用';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $user                 = User::query()->findOrFail($this->getKey());
        $user->disabled_until = $user->disabled_until ? null : Carbon::now()->addDays(7);
        $user->save();

        return $this->response()
            ->success('Processed successfully: ' . $this->getKey())
            ->refresh();
    }

    public function title()
    {
        return $this->row->disabled_until ? '启用' : '禁用';
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确定要' . $this->title() . '此用户吗？', !$this->row->disabled_until ? '此用户将被禁用7天' : ''];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
