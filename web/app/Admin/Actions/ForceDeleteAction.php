<?php

namespace App\Admin\Actions;

use Dcat\Admin\Grid\RowAction;
use Illuminate\Database\Eloquent\Model;

class ForceDeleteAction extends RowAction
{
    public $name = '彻底删除';

    public function handle(Model $model)
    {
        $model->forceDelete();
        return $this->response()->success('彻底删除成功')->refresh();
    }

    public function dialog()
    {
        $this->confirm('确定要彻底删除这条记录吗？此操作不可恢复！');
    }
}
