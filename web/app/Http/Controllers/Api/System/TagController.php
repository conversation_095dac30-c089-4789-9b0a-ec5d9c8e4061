<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\TagStoreRequest;
use App\Http\Requests\TagUpdateRequest;
use App\Models\Tag;
use App\Utils\Http\Api;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class TagController extends Controller
{
    public function index(Request $request)
    {
        $tags = Tag::query()
            ->when($request->get('name'), function ($query, $name) {
                $query->where('name', 'like', "%$name%");
            })
            ->paginate(page_size_resolver(100));

        return Api::okay($tags);
    }

    public function store(TagStoreRequest $request)
    {
        return Api::okay(Tag::query()->create($request->validated()));
    }

    public function update(Tag $tag, TagUpdateRequest $request)
    {
        $updates = $request->validated();
        if (isset($updates['enabled'])) {
            $updates['disabled_at'] = Arr::get($updates, 'enabled') ? null : Carbon::now();
        }

        $tag->update($updates);

        return Api::okay();
    }
}
