<?php

namespace App\Admin\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StickerPack;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StickerPackController extends Controller
{
    public function index(Request $request)
    {
        $queries = $this->validate($request, [
            'q' => 'required',
        ]);

        return StickerPack::query()
            ->where('name', 'like', "%{$queries['q']}%")
            ->paginate(null, DB::raw('id,name as text'));
    }
}
