<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property Collection|iterable<int, EventRegistrationValue[]> $values
 */
class EventRegistration extends Model
{
    use HasFactory;

    protected $table = 'event_registrations';

    protected $fillable = ['user_id', 'form_id'];

    public function fields(): HasMany
    {
        return $this->hasMany(EventRegistrationField::class, 'registration_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function values(): HasMany
    {
        return $this->hasMany(EventRegistrationValue::class, 'registration_id');
    }
}
