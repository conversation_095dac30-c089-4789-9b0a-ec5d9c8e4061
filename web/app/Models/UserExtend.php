<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserExtend extends Model
{
    protected $table        = 'user_extends';
    protected $primaryKey   = 'user_id';
    public    $incrementing = false;
    protected $fillable     = ['points', 'system_info'];
    protected $casts        = [
        'points'      => 'decimal:2',
        'system_info' => 'array'
    ];
    protected $hidden       = ['user_id', self::CREATED_AT, self::UPDATED_AT];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
