<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Utils\Http\Api;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class UserController extends Controller
{
    public function home($id)
    {
        return Response::redirectToRoute('user.summary', ['id' => $id]);
    }

    public function center()
    {
        return view('user.center');
    }

    public function summary($id, Request $request)
    {
        $currentUser = User::query()->findOrFail($id);
        /** @var User $user */
        $user     = $request->user();
        $followed = $user && $user->following()->find($id);

        return view('user.summary', compact('currentUser', 'followed'));
    }

    public function following($id, Request $request)
    {
        $currentUser = User::findOrFail($id);
        $following   = $currentUser->following;
        $myFollows   = [];
        if ($user = $request->user()) {
            $userTable = (new User())->getTable();
            $myFollows = $user->following()->whereIn($userTable . '.id', $following->pluck('id'))->pluck($userTable . '.id')->all();
        }
        $following->transform(fn(User $user) => tap($user, fn(User $user) => $user['followed'] = in_array($user->getKey(), $myFollows)));
        return view('user.following', [
            'currentUser' => $currentUser,
            'following'   => $following,
            'followed'    => $user && $user->following()->find($id),
        ]);
    }

    public function followers($id, Request $request)
    {
        $currentUser = User::findOrFail($id);
        $followers   = $currentUser->followers()->get();
        $myFollows   = [];
        if ($user = $request->user()) {
            $userTable = (new User())->getTable();
            $myFollows = $user->following()->whereIn($userTable . '.id', $followers->pluck('id'))->pluck($userTable . '.id')->all();
        }
        $followers->transform(fn(User $user) => tap($user, fn(User $user) => $user['followed'] = in_array($user->getKey(), $myFollows)));
        return view('user.followers', [
            'currentUser' => $currentUser,
            'followers'   => $followers,
            'followed'    => $user && $user->following()->find($id),
        ]);
    }

    public function articles($userId, Request $request)
    {
        $currentUser = User::findOrFail($userId);
        $user        = $request->user();
        $articles    = $currentUser->articles()
            ->when(!$currentUser->is($user), fn($query) => $query->where('draft', false))
            ->latest()
            ->simplePaginate();

        return view('user.articles', [
            'currentUser' => $currentUser,
            'articles'    => $articles,
            'followed'    => $user && $user->following()->find($userId),
        ]);
    }

    public function info(Request $request): JsonResponse
    {
        /** @var User $user */
        return Api::okay([
            'user'  => $user = $request->user(),
            'token' => $user->createToken('api')->accessToken,
        ]);
    }

    public function settings(Request $request)
    {
        $user = $request->user();
        return view('user.settings', [
            'currentUser' => $user,
            'user'        => $user,
        ]);
    }

    public function updateSettings(Request $request)
    {
        $user = $request->user();

        $validated = $this->validate($request, [
            'name'     => 'required|string|max:255',
            'nickname' => 'required|string|max:255',
            'bio'      => 'nullable|string|max:500',
            'gender'   => ['nullable', Rule::enum(\App\Enum\Gender::class)],
        ]);

        $user->update($validated);

        return redirect()->back()->with('success', '个人信息更新成功');
    }

    public function updateEmail(Request $request)
    {
        $user = $request->user();

        $validated = $this->validate($request, [
            'email'    => 'required|email|unique:users,email,' . $user->id,
            'password' => 'required|string',
        ]);

        // Verify current password
        if (!Hash::check($validated['password'], $user->password)) {
            return redirect()->back()->withErrors(['password' => '当前密码不正确']);
        }

        $user->update(['email' => $validated['email']]);

        return redirect()->back()->with('success', '邮箱更新成功');
    }
}
