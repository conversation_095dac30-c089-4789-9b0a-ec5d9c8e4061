<?php

namespace App\Http\Requests;

use App\Enum\TodoStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TodoStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title'       => 'required|string|max:255',
            'description' => 'string|max:2000',
            'status'      => [Rule::enum(TodoStatus::class)],
            'deadline'    => 'date',
        ];
    }

    public function attributes()
    {
        $id = 'models/todo.attributes';

        return trans()->has($id) ? trans($id) : [];
    }
}
