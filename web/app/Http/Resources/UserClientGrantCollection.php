<?php

namespace App\Http\Resources;

use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class UserClientGrantCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return $this->collection->map(function (Client $client) {
            return [
                'id'         => $client->getKey(),
                'name'       => $client->name,
                'logo'       => $client->logo,
                'granted_at' => $client->pivot->updated_at,
            ];
        })->all();
    }
}
