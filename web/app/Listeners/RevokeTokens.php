<?php

namespace App\Listeners;

use App\Models\User;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class RevokeTokens implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        /** @var User $user */
        switch (true) {
            case $event instanceof PasswordReset:
            case $event instanceof Logout:
                $user = $event->user;
                break;
        }
        foreach ($user->tokens as $token) {
            $token->revoke();
        }
    }
}
