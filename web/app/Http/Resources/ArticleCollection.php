<?php

namespace App\Http\Resources;

use App\Utils\Eloquent\Resource\BaseCollection;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * @mixin LengthAwarePaginator
 */
class ArticleCollection extends BaseCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request)
    {
        $this->collection
            ->transform(function (ArticleResource $article) use ($request) {
                return new ArticleResource($article->makeHidden(['content']));
            });

        return parent::toArray($request);
    }
}
