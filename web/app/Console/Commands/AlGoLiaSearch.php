<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Algolia\AlgoliaSearch\SearchClient;
use App\Models\Article;

class AlGoLiaSearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:al-go-lia-search';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = SearchClient::create("NSQAPII14R", "885aa808cc4b4cd9ee00664741857c36");
        $index = $client->initIndex("articles");
        $articles = Article::all()->transform(function (Article $article) {
            $article['content'] = mb_substr($article->content, 0, 1000);
            $article['cover'] = storage_url($article->cover);
            return $article;
        });
        $index->saveObjects($articles->toArray(), ['autoGenerateObjectIDIfNotExist' => true]);
    }
}
