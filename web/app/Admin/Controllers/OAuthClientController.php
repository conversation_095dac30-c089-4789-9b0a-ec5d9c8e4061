<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\AvatarWithName;
use App\Admin\Repositories\OauthClient;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class OAuthClientController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new OauthClient(), function (Grid $grid) {
            $grid->model()->where('revoked', false);
            $grid->column('user')->displayUsing(AvatarWithName::class);
            $grid->column('name');
            $grid->column('logo')->image();
            $grid->column('personal_access_client')->switch();
            $grid->column('password_client')->switch();
            $grid->column('skips_authorization')->switch();
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new OauthClient(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('name');
            $show->field('description');
            $show->field('homepage');
            $show->image('logo');
            $show->field('secret');
            $show->field('provider');
            $show->field('redirect');
            $show->field('personal_access_client');
            $show->field('password_client');
            $show->field('skips_authorization');
            $show->field('scopes');
            $show->field('revoked');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new OauthClient(), function (Form $form) {
            $form->text('name');
            $form->textarea('description');
            $form->text('homepage');
            $form->image('logo');
            $form->display('id');
            $form->display('secret');
            $form->textarea('redirect')->placeholder('多个使用逗号分割');
            $form->switch('personal_access_client');
            $form->switch('password_client');
            $form->switch('skips_authorization');
            $form->text('scopes');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
