<?php

namespace App\Http\Controllers\Api;

use App\Enum\Gender;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserUpdateRequest;
use App\Http\Resources\ArticleCollection;
use App\Http\Resources\StickerCollection;
use App\Models\Favorite;
use App\Models\User;
use App\Models\UserCheckIn;
use App\Models\UserEvent;
use App\Utils\Http\Api;
use App\Utils\Uploader;
use BadMethodCallException;
use Carbon\Carbon;
use DateTimeInterface;
use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;
use OpenApi\Attributes as OA;
use Throwable;

class UserController extends Controller
{
    /**
     * @throws Throwable
     */
    public function checkin(Request $request)
    {
        /** @var User $user */
        $user   = $request->user();
        $userId = $user->getKey();

        DB::transaction(function () use ($userId, $user) {
            if (UserCheckIn::query()
                ->whereDate('checkin_at', now())
                ->where('user_id', $userId)->lockForUpdate()
                ->exists()
            ) {
                throw new Exception('您今天已经打卡了，不能重复打卡！');
            }

            UserCheckIn::query()->create([
                'user_id'    => $userId,
                'checkin_at' => now(),
                'ranking'    => UserCheckIn::query()->whereDate('checkin_at', now()->toDateString())->max('ranking') + 1,
            ]);
            if (!UserCheckIn::query()->whereDate('checkin_at', now()->subDay())->exists()) {
                $userCheckInDays = 1;
            } else {
//                $userCheckInDays = (int)$user->extraInfo?->checkin_days + 1;
            }
        });

        return Api::okay(message: '签到成功');
    }

    /**
     * @throws ValidationException
     */
    public function upload(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'name' => 'string',
            'file' => 'required|file|max:' . 1 << 21, // 大小2MB
        ], [
            'file.max' => '文件大小不能超过2MB',
        ]);

        $uploadedFile = $data['file'];
        $file         = Uploader::upload($uploadedFile);

        return Api::okay($file);
    }

    #[OA\Get(
        path: '/api/v1/user/profile',
        summary: '获取用户信息',
        security: [['api' => []]],
        tags: ['用户'],
        responses: [
            new OA\Response(response: 200, description: 'ok', content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'code', description: '错误码', type: 'int'),
                    new OA\Property(property: 'data', description: '数据', properties: [
                        new OA\Property(property: 'id', description: '用户uuid', type: 'string'),
                        new OA\Property(property: 'name', description: '姓名', type: 'string'),
                        new OA\Property(property: 'gender', description: '性别: 1保密，2男，3女，4沃尔玛购物袋', type: 'int', enum: Gender::class),
                        new OA\Property(property: 'nickname', description: '昵称', type: 'string'),
                        new OA\Property(property: 'avatar', description: '头像', type: 'string'),
                        new OA\Property(property: 'email', description: '邮箱', type: 'string'),
                    ], type: 'object'),
                ],
            )),
        ]
    )]
    public function profile(Request $request): JsonResponse
    {
        $user = $request->user();

        return Api::okay($user->load('extends'));
    }

    /**
     * @throws ValidationException
     * @throws AuthenticationException
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'old_password' => 'required',
            'password'     => 'required|confirmed',
        ], [], __('models/user.attributes'));

        $user        = $request->user();
        $oldPassword = $request->input('old_password');
        if ($user->password && !Hash::check($oldPassword, $user->password)) {
            throw new InvalidArgumentException('原密码不正确');
        }
        if (Hash::check($data['password'], $user->password)) {
            throw new InvalidArgumentException('新密码不能和原密码相同');
        }

        $user->update(['password' => $newPassword = Hash::make($data['password'])]);
        event(new PasswordReset($user));
        auth('web')->logoutOtherDevices($newPassword);

        return Api::okay();
    }

    /**
     * @throws ValidationException
     */
    public function emailVerificationNotification(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'email' => 'required|email',
        ], [], __('models/user.attributes'));

        $user = $request->user();
        if ($data['email'] == $user->email && $user->hasVerifiedEmail()) {
            return Api::fail('该邮箱已经通过验证，不需要重复验证');
        }

        $user->sendEmailVerificationNotification();

        return Api::okay(message: '发送成功');
    }

    public function follow(User $user, Request $request): JsonResponse
    {
        /** @var User $currenUser */
        $currenUser = $request->user();
        if ($currenUser->is($user)) {
            throw new BadMethodCallException('不能操作自己');
        }
        $userId = $currenUser->getKey();
        if ($user->followers()->find($userId)) {
            $user->followers()->detach($userId);
        } else {
            $user->followers()->attach($userId);
        }

        return Api::okay();
    }

    public function notifications(Request $request)
    {
        $notifications = $request->user()
            ->notifications()
            ->whereNull('read_at')
            ->paginate();

        return Api::okay($notifications);
    }

    public function updateProfile(UserUpdateRequest $request)
    {
        $request->user()->update($request->validated());

        return Api::okay(User::find($request->user()->getKey()));
    }

    /**
     * @throws ValidationException
     */
    public function changeEmail(Request $request): JsonResponse
    {
        $input = $this->validate($request, [
            'email'   => 'required|email',
            'captcha' => 'required|captcha'
        ]);
        /** @var User $user */
        $user = $request->user();
        if ($user->email === $email = $input['email']) {
            throw new Exception('新邮箱不能和旧邮箱相同');
        }
        if (User::query()->where('email', $email)->where('id', '!=', $user->getKey())->exists()) {
            throw ValidationException::withMessages([
                'email' => __('validation.unique', ['attribute' => 'email']),
            ]);
        }
        $user->update(['email' => $email]);

        return Api::okay();
    }

    public function imSignature(Request $request)
    {
        /** @var User $user */
        $user      = $request->user();
        $userId    = (string)$user->getKey();
        $appId     = '1';
        $origin    = [
            'AppID'   => $appId,
            'UserID'  => $userId,
            'Expires' => $expires = Carbon::now()->addDay()->format(DateTimeInterface::RFC3339),
        ];
        $key       = 'kYeOD6YozrXF9w2Czz6kjJoXCwi2VyvA';
        $signature = hash_hmac('sha256', base64_encode(http_build_query($origin)), $key);

        return Api::okay(compact('signature', 'expires'));
    }

    public function points(Request $request)
    {
        $user   = $request->user();
        $result = $user->events()
            ->where('points', '!=', 0)
            ->latest('id')
            ->whereBetween(UserEvent::CREATED_AT, [Carbon::now()->subMonth()->toDateTimeString(), Carbon::now()->toDateTimeString()])
            ->simplePaginate();

        return Api::okay($result);
    }

    /**
     * @throws ValidationException
     */
    public function favorites(Request $request): JsonResponse
    {
        $this->validate($request, [
            'type' => 'in:article,sticker',
        ]);
        $user = $request->user();

        $fetcher = fn($query) => $query->orderByPivot(Favorite::CREATED_AT, 'DESC')
            ->simplePaginate(page_size_resolver(21, 21)());

        $favorites = match ($request->input('type', 'sticker')) {
            'article' => new ArticleCollection($fetcher($user->favoritedArticles()->with(['author:id,uuid,nickname,avatar']))),
            'sticker' => new StickerCollection($fetcher($user->favoritedStickers()->with('pack'))),
        };

        return Api::okay($favorites);
    }
}
