<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\ForceDeleteAction;
use App\Admin\Actions\Grid\SwitchGridView;
use App\Admin\Actions\RestoreAction;
use App\Admin\Columns\Date;
use App\Admin\Repositories\Sticker;
use App\Models\Category;
use App\Models\StickerPack;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class StickerController extends AdminController
{
    // 用于缓存分类数据
    protected static $categoriesCache = null;

    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Sticker::with(['categories.parent', 'pack']), function (Grid $grid) {
            if (request()->get('_view_') !== 'list') {
                $grid->view('admin.grid.image');
            }

            $grid->tools([
                new SwitchGridView(),
            ]);

            $isTrash = \request('_scope_') === 'trashed';
            $grid->with(['trashed' => $isTrash]);
            $grid->model()
                ->orderByDesc('created_at')
                ->when($isTrash, fn(Builder $query) => $query->onlyTrashed());

            $grid->setActionClass(Grid\Displayers\Actions::class);
            $grid->column('image')->image(width: 100, height: 100);
            $grid->column('name')->editable();
            $grid->column('pack.name', '相册')->display(function ($name) {
                if ($this->pack_id) {
                    $href = route('dcat.admin.sticker-packs.show', $this->pack_id);
                    return "<a href=\"$href\">{$name}</a>";
                }

                return '/';
            });
            $grid->column('categories')
                ->display(function (Collection $categories) {
                    return $categories->map(function (Category $category) {
                        $categoryName = $category->name;
                        if ($parent = $category->parent) {
                            $categoryName = $parent->name . '->' . $categoryName;
                        }
                        return $categoryName;
                    })->join('<br>');
                });

            $grid->column('views')->sortable();
            $grid->column('created_at')->displayUsing(Date::class)->sortable();

            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            if ($isTrash) {
                $grid->column('deleted_at', '删除时间')->displayUsing(Date::class);
                $grid->disableCreateButton();
            }

            // 添加跳转到sticker-packs页面的按钮
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<div class="btn-group">
                    <a href="' . admin_url('sticker-packs') . '" class="btn btn-mini btn-primary" title="跳转到表情包页面">
                        <i class="feather icon-package"></i><span class="d-none d-sm-inline">&nbsp;表情包管理</span>
                    </a>
                </div>');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) use ($isTrash) {
                if ($isTrash) {
                    $actions->append(new RestoreAction()); // 恢复按钮
//                    $actions->append(new ForceDeleteAction()); // 彻底删除按钮
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->whereNotNull('deleted_at');
                $filter->equal('id');
                $filter->where('categories', function (Builder $query) {
                    if ($category = Category::query()->find($this->input)) {
                        if (!$category->parent_id) {
                            $query->whereHas('categories',
                                fn(Builder $query) => $query
                                    ->where('parent_id', $category->parent_id)
                                    ->orWhere('id', $category->getKey()));
                        } else {
                            $query->whereHas('categories',
                                fn(Builder $query) => $query
                                    ->where('id', $category->getKey()));
                        }
                    }
                })->select(app(\App\Admin\Repositories\Category::class)->getCategories(false));
            });
        });
    }

    /**
     * 获取所有启用的分类数据（带缓存）
     *
     * @return Collection
     */
    public static function getCategoriesWithCache()
    {
        if (self::$categoriesCache === null) {
            self::$categoriesCache = Category::enabled()
                ->with('children')
                ->whereNull('parent_id')
                ->orderBy('order')
                ->get(['id', 'name', 'parent_id']);
        }

        return self::$categoriesCache;
    }

    /**
     * 获取分类数据的API接口
     *
     * @param Request $request
     *
     * @return \Dcat\Admin\Http\JsonResponse
     */
    public function categories(Request $request)
    {
        $categories = self::getCategoriesWithCache();

        return $this->response()
            ->success('')
            ->data(['categories' => $categories]);
    }

    /**
     * 获取贴纸当前分类的API接口
     *
     * @param Request $request
     *
     * @return \Dcat\Admin\Http\JsonResponse
     */
    public function stickerCategories(Request $request)
    {
        $stickerId   = $request->get('sticker_id');
        $categoryIds = [];

        if ($stickerId) {
            $sticker = \App\Models\Sticker::find($stickerId);
            if ($sticker) {
                $categoryIds = $sticker->categories->pluck('id')->toArray();
            }
        }

        return $this->response()
            ->success('')
            ->data(['category_ids' => $categoryIds]);
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Sticker(), function (Show $show) {
            $show->field('user_id');
            $show->field('pack_id');
            $show->field('name');
            $show->field('image')->image();
            $show->field('views');
            $show->field('created_at');
            $show->field('updated_at');
            $show->field('deleted_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Sticker(), function (Form $form) {
            $form->ignore(['image_type', 'parent']);
            $form->select('categories')
                ->options(app(\App\Admin\Repositories\Category::class)->getCategories(false))
                ->required()
                ->value($form->model()->categories()->pluck('id')->first());
            $form->text('name');
            $form->radio('image_type')
                ->value(1)
                ->when(1, function (Form $form) {
                    $form->image('image')->required();
                })
                ->when(2, function (Form $form) {
                    $form->text('image')->required();
                })
                ->options([1 => '图片上传', 2 => '图片链接']);
            $authorSelector = $form->select('pack_id', 'Pack')
                ->options(StickerPack::class, 'id', 'name')
                ->ajax('api/sticker-packs');
            if ($form->isEditing()) {
                $authorSelector->value($form->model()->pack_id);
            }
        });
    }
}
