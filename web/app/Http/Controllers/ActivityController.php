<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\EventRegistrationForm;
use App\Utils\Http\Api;
use App\Utils\ViewRecorder\Recorder;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivityController extends Controller
{
    public function index()
    {
        $events = Activity::query()
            ->where('state', Activity::STATE['living'])
            ->whereHas('user')
            ->latest('id')
            ->with(['user'])
            ->take(10)
            ->get();

        return view('event.index', compact('events'));
    }

    /**
     * @throws Exception
     */
    public function show(Activity $event, Request $request)
    {
        if (!$event->isState(Activity::STATE['living'])) {
            throw new Exception('房间未开启');
        }
        $event->load(['user']);
        $view = view('event.show', compact('event'));
        if ($user = Auth::user()) {
            $view->with('token', $user->createToken('api')->accessToken);
        }

        $event->views = app(Recorder::class)->record($event, $request);

        return $view;
    }

    /**
     * @throws Exception
     */
    public function getRegister(Activity $event, $formId, Request $request)
    {
        /** @var EventRegistrationForm $form */
        if (!$form = $event->registrationForms()->findOrFail($formId)) {
            abort(404);
        }

        $form->load(['fields']);
        $user         = $request->user();
        $registration = $user ? $form->registrations()->with(['values'])->where('user_id', $user->getKey())->first() : null;
        return view('event.register', compact('event', 'registration', 'user', 'form'));
    }

    /**
     * @throws Exception
     */
    public function postRegister(Activity $event, $formId, Request $request)
    {
        /** @var EventRegistrationForm $form */
        if (!$form = $event->registrationForms()->findOrFail($formId)) {
            abort(404);
        }
        if (!$form->enable) {
            throw new Exception('该活动未开启报名');
        }

        $registration = $form->registrations()->updateOrCreate(['user_id' => $request->user()->getKey(), 'form_id' => $form->getKey()]);
        $input        = $request->input();
        foreach ($input as $key => $value) {
            $registration->values()->updateOrCreate(['name' => $key], ['form_id' => $form->getKey(), 'value' => $value,]);
        }

        return Api::okay();
    }
}
