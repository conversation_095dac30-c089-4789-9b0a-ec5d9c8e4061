<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int    $id
 * @property string $uuid
 * @property string $name
 * @property Carbon $disabled_at
 */
class Tag extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $table    = 'tags';
    protected $fillable = ['name', 'icon', 'disabled_at'];
    protected $hidden   = ['pivot', self::DELETED_AT];
    protected $casts    = ['disabled_at' => 'datetime'];

    public function articles(): MorphToMany
    {
        return $this->morphedByMany(Article::class, 'model', 'model_has_tags', 'tag_id', null, 'id', 'id');
    }

    public function stickers(): MorphToMany
    {
        return $this->morphedByMany(Sticker::class, 'model', 'model_has_tags', 'tag_id', null, 'id', 'id');
    }

    public function toSearchableArray()
    {
        return $this->only(['name', 'icon']);
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }
}
