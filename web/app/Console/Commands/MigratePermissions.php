<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;

class MigratePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        /** @var \Illuminate\Routing\Router $router */
        $router = app('router');
        DB::statement('set FOREIGN_KEY_CHECKS=0');
        Permission::query()->truncate();
        DB::statement('set FOREIGN_KEY_CHECKS=1');
        $namedRoutes = $router->getRoutes()->getRoutesByName();
        foreach ($namedRoutes as $uri => $namedRoute) {
            if (str_starts_with($uri, 'api.system') && !str_ends_with($uri, '.')) {
                Permission::query()->firstOrCreate([
                    'name'       => $uri,
                    'guard_name' => 'api'
                ]);
            }
        }
    }
}
