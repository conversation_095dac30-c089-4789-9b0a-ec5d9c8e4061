<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use OpenApi\Attributes as OA;

#[OA\Info(version: 0.1, title: 'Passport')]
#[OA\SecurityScheme(securityScheme: 'Token', type: 'http', bearerFormat: 'JWT', scheme: 'bearer')]
abstract class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    protected ?User $user;

    public function __construct()
    {
        $this->middleware(function (Request $request, \Closure $next) {
            $this->user = $request->user();

            return $next($request);
        });
    }
}
