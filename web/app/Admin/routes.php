<?php

use App\Admin\Controllers\Api;
use App\Admin\Controllers\Api\UploadController;
use Dcat\Admin\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {
    $router->get('/', [\App\Admin\Controllers\HomeController::class, 'index']);
    $router->resource('stickers', \App\Admin\Controllers\StickerController::class);
    $router->resource('sticker-packs', \App\Admin\Controllers\StickerPackController::class);
    $router->get('stickers/categories', [\App\Admin\Controllers\StickerController::class, 'categories']);
    $router->get('stickers/sticker-categories', [\App\Admin\Controllers\StickerController::class, 'stickerCategories']);
    $router->resource('categories', \App\Admin\Controllers\CategoryController::class);
    $router->resource('tags', \App\Admin\Controllers\TagController::class);
    $router->resource('articles', \App\Admin\Controllers\ArticleController::class);
    $router->resource('todos', \App\Admin\Controllers\TodoController::class);
    $router->resource('feedbacks', \App\Admin\Controllers\FeedbackController::class);
    $router->resource('favorites', \App\Admin\Controllers\FavoriteController::class);
    $router->resource('api-keys', \App\Admin\Controllers\ApiKeyController::class);
    $router->resource('search-logs', \App\Admin\Controllers\SearchLogController::class);

    $router->resource('wechat-apps', \App\Admin\Controllers\WechatAppController::class);
    $router->get('verification-codes', [\App\Admin\Controllers\VerificationCodeController::class, 'index']);
    $router->resource('user', \App\Admin\Controllers\UserController::class);
    $router->resource('oauth-clients', \App\Admin\Controllers\OAuthClientController::class);
    $router->resource('failed-jobs', \App\Admin\Controllers\FailedJobController::class)->only(['index', 'show', 'destroy']);
    $router->resource('system-logs', \App\Admin\Controllers\SystemLogController::class);

    $router->group(['prefix' => 'api', 'as' => 'api.'], function () use ($router) {
        $router->apiResource('users', Api\UserController::class);
        $router->apiResource('categories', Api\CategoryController::class);
        $router->apiResource('sticker-packs', Api\StickerPackController::class);
        $router->post('upload/images', [UploadController::class, 'images']);
    });

    // 导航管理
    $router->group(['prefix' => 'navigation', 'namespace' => 'Navigation',], function (Router $router) {
        //网站管理
        $router->resource('site', 'SiteController');
        // 分类管理
        $router->group(['prefix' => 'category'], function (Router $router) {
            $router->get('/getParent', 'SiteCategoryController@getParent');
            $router->get('/allSearch', 'SiteCategoryController@searchChildren');
        });
        $router->resource('category', 'SiteCategoryController');
        // 标签管理
        $router->group(['prefix' => 'tag'], function (Router $router) {
            $router->get('/allSearch', 'SiteTagController@search');
        });
        $router->resource('tag', 'SiteTagController');
        // 配置
        $router->resource('config', 'ConfigController');
        // 轮播消息
        $router->resource('message', 'MessageController');
        // 用户建议
        $router->resource('suggest', 'SuggestController');
        // 搜索
        $router->resource('search', 'SearchController');
        // 搜索日志
        $router->get('/searchRecord', 'SearchRecordController@index');
    });

    // 内容管理
    $router->group(['prefix' => 'content', 'namespace' => 'Content',], function (Router $router) {
        // 文章管理
        $router->group(['prefix' => 'article'], function (Router $router) {
            $router->get('/search', 'ArticleController@search');
        });
        $router->resource('article', 'ArticleController');
        // 视频管理
        $router->group(['prefix' => 'video'], function (Router $router) {
            $router->get('/search', 'VideoController@search');
        });
        $router->resource('video', 'VideoController');
        // 图集管理
        $router->group(['prefix' => 'picture'], function (Router $router) {
            $router->get('/search', 'PictureController@search');
        });
        $router->resource('picture', 'PictureController');
    });
});
