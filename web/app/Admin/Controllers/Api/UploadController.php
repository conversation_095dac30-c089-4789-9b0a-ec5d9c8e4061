<?php

namespace App\Admin\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Utils\Uploader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UploadController extends Controller
{
    /**
     * todo 安全验证
     * @param Request $request
     *
     * @return array[]
     * @throws \Illuminate\Validation\ValidationException
     */
    public function images(Request $request)
    {
        $input = $this->validate($request, [
            'image' => ['required', 'file', 'max:' . 20 * 1024 * 1024, 'mimes:jpeg,png,jpg,gif']
        ]);
        $file  = Uploader::upload($input['image']);

        return [
            'data' => [
                'filePath' => Storage::url($file->path),
            ]
        ];
    }
}
