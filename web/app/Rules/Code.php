<?php

namespace App\Rules;

use App\Enum\VerificationCodeType;
use App\Models\VerificationCode;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Arr;
use InvalidArgumentException;

class Code implements ValidationRule, DataAwareRule
{
    protected string $identity = '';

    public function __construct(
        protected VerificationCodeType $type,
    )
    {
    }

    public function setData(array $data)
    {
        $this->identity = Arr::get($data, strtolower($this->type->name));

        return $this;
    }

    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($this->identity)) {
            throw new InvalidArgumentException('缺少参数：' . $this->type->value);
        }
        if (!$code = VerificationCode::available()
            ->where('type', $this->type)
            ->where('identity', $this->identity)
            ->first()
        ) {
            $fail('请先获取验证码');
            return;
        }

        if (strcasecmp($code->code, $value) !== 0) {
            $fail(' 验证码不正确');
            return;
        }
        $code->delete();
    }
}
