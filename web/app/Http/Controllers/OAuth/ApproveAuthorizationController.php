<?php

namespace App\Http\Controllers\OAuth;

use App\Models\UserClientGrant;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Passport\Http\Controllers\ApproveAuthorizationController as PassportApproveAuthorizationController;
use <PERSON><PERSON>holm\Psr7\Response as Psr7Response;

class ApproveAuthorizationController extends PassportApproveAuthorizationController
{
    /**
     * Approve the authorization request.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function approve(Request $request)
    {
        $this->assertValidAuthToken($request);

        $authRequest = $this->getAuthRequestFromSession($request);

        $authRequest->setAuthorizationApproved(true);

        $response = $this->withErrorHandling(function () use ($authRequest) {
            return $this->convertResponse(
                $this->server->completeAuthorizationRequest($authRequest, new Psr7Response)
            );
        });

        $attributes = [
            'user_id'   => $authRequest->getUser()->getIdentifier(),
            'client_id' => $authRequest->getClient()->getIdentifier(),
        ];
        if (!$userClient = UserClientGrant::query()->where($attributes)->first()) {
            UserClientGrant::query()->create($attributes);
        } else {
            $userClient->touch();
        }

        return $response;
    }
}
