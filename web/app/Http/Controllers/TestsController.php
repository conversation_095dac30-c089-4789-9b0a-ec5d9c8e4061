<?php

namespace App\Http\Controllers;

use MongoDB\Client;

class TestsController extends Controller
{
    public function mongo()
    {
        $client     = new Client('***********************************************************************************');
        $collection = $client->sample_mflix->movies;
        $filter     = ['title' => 'The Shawshank Redemption'];
        $result     = $collection->findOne($filter);
        if ($result) {
            echo json_encode($result, JSON_PRETTY_PRINT);
        } else {
            echo 'Document not found';
        }
    }
}
