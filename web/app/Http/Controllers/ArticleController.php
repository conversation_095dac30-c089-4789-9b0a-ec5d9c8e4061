<?php

namespace App\Http\Controllers;

use App\Enum\ArticleFlag;
use App\Http\Requests\ArticleStoreRequest;
use App\Models\Article;
use App\Models\User;
use App\Models\UserCheckIn;
use App\Utils\ViewRecorder\Recorder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ArticleController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->middleware('auth')->only('create', 'store', 'edit', 'update', 'destroy');
    }

    public function index(Request $request)
    {
        $checkIn = null;
        if ($user = $request->user()) {
            $checkIn = UserCheckIn::query()
                ->where('user_id', $user->getKey())
                ->whereDate('checkin_at', now()->toDateString())
                ->first();
        }

        $checkInsCount = UserCheckIn::query()
            ->whereDate('checkin_at', now()->toDateString())
            ->count();
        $articles      = Article::with(['user', 'tags'])
            ->where('draft', 0)
            ->withCount(['comments'])
            ->when($request->query('query'), fn(Builder $query, $keyword) => $query->where('title->' . app()->getLocale(), 'like', "%$keyword%"))
            ->latest()
            ->simplePaginate()
            ->appends($request->only('query'));

        return view('articles.index', compact('checkIn', 'checkInsCount', 'articles'));
    }

    public function show($id, Recorder $recorder, Request $request)
    {
        /** @var Article $article */
        $article  = Article::query()->where('uuid', $id)->firstOrFail();
        $comments = $article->comments()
            ->where('parent_id', '')
            ->with(['user', 'children.user'])
            ->simplePaginate();
        $featured = Article::featured()->with(['user'])->visible()->take(10)->get();
        if (($article->flag & ArticleFlag::needLoginWhenRead->value) === 0 || $request->user()) {
            $article->views = $recorder->record($article, $request);
        }

        return view('articles.show', compact('article', 'featured', 'comments'));
    }

    public function create()
    {
        return view('articles.create');
    }

    public function store(ArticleStoreRequest $request)
    {
        $article = $request->user()->articles()->create($request->validated());

        return redirect()->route('articles.show', ['article' => $article->uuid]);
    }

    public function edit($id, Request $request)
    {
        /** @var User $user */
        $user    = $request->user();
        $article = $user->articles()->where('uuid', $id)->firstOrFail();

        return view('articles.edit', ['article' => $article]);
    }

    public function update($id, Request $request)
    {
        $user = $request->user();
        /** @var Article $article */
        $article = $user->articles()->where('uuid', $id)->firstOrFail();
        $article->update([
            'title'   => $request->input('title'),
            'content' => $request->input('content'),
        ]);

        return redirect()->route('articles.show', ['article' => $article->uuid]);
    }
}
