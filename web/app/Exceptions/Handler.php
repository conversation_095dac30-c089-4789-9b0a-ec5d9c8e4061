<?php

namespace App\Exceptions;

use App\Enum\ApiCode;
use App\Utils\Http\Api;
use Exception;
use Fig\Http\Message\StatusCodeInterface;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash  = [
        'current_password',
        'password',
        'password_confirmation',
    ];
    protected $dontReport = [
        BusinessException::class,
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->renderable(fn(AccessDeniedHttpException $e) => throw new BusinessException(trans('response.user.permission_denied'), StatusCodeInterface::STATUS_FORBIDDEN));
        $this->renderable(fn(ModelNotFoundException $e) => throw new BusinessException('资源信息出错'));
        $this->renderable(fn(QueryException $e) => throw new Exception(app()->hasDebugModeEnabled() ? $e->getMessage() : '数据库错误'));
        $this->renderable(fn(InvalidArgumentException $e) => throw new BusinessException($e->getMessage())); // 默认code 400
        $this->renderable(fn(ThrottleRequestsException $e) => throw new BusinessException('请求过快，请一分钟后再重新尝试'));
    }

    protected function prepareJsonResponse($request, Throwable $e): JsonResponse
    {
        $isHttpException = $this->isHttpException($e);
        return Api::fail(
            $e->getMessage(),
            code: $isHttpException ? $e->getStatusCode() : ((int)$e->getCode() ?: ApiCode::CommonError->value),
        )->withHeaders($isHttpException ? $e->getHeaders() : []);
    }

    protected function invalidJson($request, ValidationException $exception): JsonResponse
    {
        return Api::fail($exception->getMessage(), 422)->setStatusCode($exception->status);
    }

    protected function unauthenticated($request, AuthenticationException $exception)
    {
        return $this->shouldReturnJson($request, $exception)
            ? Api::fail($exception->getMessage(), StatusCodeInterface::STATUS_UNAUTHORIZED)
                ->setStatusCode(StatusCodeInterface::STATUS_UNAUTHORIZED)
            : redirect()->guest($exception->redirectTo($request) ?:
                route('login', ['redirect_uri' => $request->fullUrl()])
            );
    }
}
