<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithStartRow;

class UserImport implements ToCollection, WithHeadingRow, WithStartRow, SkipsEmptyRows
{
    use Importable;

    public function collection(Collection $collection)
    {
        Validator::make($collection->toArray(), [
            '*.id'       => 'required',
            '*.name'     => 'required|max:2',
            '*.nickname' => 'max:2',
            '*.email'    => 'required|email',
        ])->validate();
        dd($collection->toArray());
    }

    public function startRow(): int
    {
        return 3;
    }
}
