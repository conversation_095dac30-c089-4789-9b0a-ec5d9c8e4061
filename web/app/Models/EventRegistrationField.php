<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventRegistrationField extends Model
{
    use HasFactory, SoftDeletes;

    protected $table    = 'event_registration_fields';
    protected $fillable = ['form_id', 'registration_id', 'name', 'type', 'label', 'attributes', 'validations', 'sort'];
    protected $hidden   = ['form_id'];
    protected $casts    = [
        'label'       => 'object',
        'validations' => 'object',
        'attributes'  => 'object',
        'sort'        => 'integer'
    ];

    public function form(): BelongsTo
    {
        return $this->belongsTo(EventRegistrationForm::class, 'id', 'form_id');
    }
}
