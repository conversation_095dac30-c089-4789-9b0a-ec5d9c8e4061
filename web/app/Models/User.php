<?php

namespace App\Models;

use App\Enum\Gender;
use App\Utils\Auth\MustVerifyPhone;
use App\Utils\Auth\MustVerifyPhoneContract;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Laragear\WebAuthn\Contracts\WebAuthnAuthenticatable;
use Laragear\WebAuthn\WebAuthnAuthentication;
use Lara<PERSON>\Passport\HasApiTokens;
use Laravel\Passport\Token;
use Spatie\Permission\Traits\HasRoles;

/**
 * @mixin Builder
 * @property int                                          $id
 * @property string                                       $uuid
 * @property string                                       $name
 * @property string                                       $nickname
 * @property string                                       $email
 * @property ?Carbon                                      $email_verified_at
 * @property string                                       $avatar
 * @property int                                          $gender
 * @property string                                       $password
 * @property string                                       $bio
 * @property Collection|iterable<int, User>               $followers
 * @property Collection|iterable<int, User>               $following
 * @property Collection|iterable<int, Todo>               $todos
 * @property Collection|iterable<int, SocialiteUser>      $socialiteUsers
 * @property Collection|iterable<int, Client>             $clientGrants
 * @property Carbon                                       $disabled_until
 * @property Carbon                                       $created_at
 * @property ?Carbon                                      $updated_at
 * @property Collection|iterable<int, Token>              $tokens
 * @property Collection|iterable<int, WebAuthnCredential> $webAuthnCredentials
 */
class User extends Authenticatable implements MustVerifyEmail, MustVerifyPhoneContract, WebAuthnAuthenticatable
{
    use HasApiTokens, HasFactory, Notifiable, MustVerifyPhone;
    use SoftDeletes, HasUuids, HasRoles;
    use WebAuthnAuthentication;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'nickname',
        'email',
        'avatar',
        'gender',
        'bio',
        'password',
        'disabled_until',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
        Model::DELETED_AT,
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'user_id'           => 'integer',
        'gender'            => Gender::class,
        'email_verified_at' => 'datetime',
        'disabled_until'    => 'datetime',
    ];

    /**
     * 社会化登录的用户
     */
    public function socialiteUsers(): HasMany
    {
        return $this->hasMany(SocialiteUser::class, 'user_id', 'id');
    }

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class, 'user_id', 'id');
    }

    public function lessons(): HasMany
    {
        return $this->hasMany(Lesson::class, 'user_id', 'id');
    }

    public function clientGrants(): BelongsToMany
    {
        $ucgt = (new UserClientGrant())->getTable();
        return $this->belongsToMany(Client::class, $ucgt, 'user_id', 'client_id', 'id')
            ->withTimestamps();
    }

    public function weChatApps(): HasMany
    {
        return $this->hasMany(WechatApp::class, 'user_id', 'id');
    }

    public function articles(): HasMany
    {
        return $this->hasMany(Article::class, 'user_id', 'id');
    }

    public function activities(): HasMany
    {
        return $this->hasMany(Activity::class);
    }

    public function events(): HasMany
    {
        return $this->hasMany(UserEvent::class, 'user_id', 'id');
    }

    public function todos(): HasMany
    {
        return $this->hasMany(Todo::class, 'user_id', 'id');
    }

    public function followers(): BelongsToMany
    {
        $ft = (new Follow())->getTable();
        return $this->belongsToMany(User::class, $ft, 'user_id', 'follow_id', 'id', 'id')
            ->withTimestamps();
    }

    public function following(): BelongsToMany
    {
        return $this->belongsToMany(User::class, (new Follow())->getTable(), 'follow_id', 'user_id', 'id', 'id')
            ->withTimestamps();
    }

    /**
     * 获取禁用信息
     *
     * @return array{0: boolean, 1: Carbon}
     */
    public function getDisabledInfo(): array
    {
        return [$this->isDisabled(), $this->disabled_until];
    }

    public function isDisabled(): bool
    {
        return $this->disabled_until && $this->disabled_until->isFuture();
    }

    public function files(): HasMany
    {
        return $this->hasMany(File::class, 'user_id', 'id');
    }

    public function loginLogs(): HasMany
    {
        return $this->hasMany(LoginLog::class, 'user_id', 'id');
    }

    public function isRoot(): bool
    {
        return $this->email === '<EMAIL>';
    }

    public function stickers(): HasMany
    {
        return $this->hasMany(Sticker::class, 'user_id', 'id');
    }

    public function favorites()
    {
        return $this->hasMany(Favorite::class, 'user_id', 'id');
    }

    public function favoritedStickers()
    {
        $ft = (new Favorite())->getTable();

        return $this->morphedByMany(Sticker::class, 'model', $ft)
            ->withTimestamps();
    }

    public function favoritedArticles()
    {
        $ft = (new Favorite())->getTable();

        return $this->morphedByMany(Article::class, 'model', $ft)
            ->withTimestamps();
    }

    public function extends(): HasOne
    {
        return $this->hasOne(UserExtend::class, 'user_id', 'id');
    }

    /**
     * 查找给定用户名的用户实例。
     */
    public function findForPassport($username): ?static
    {
        return $this->where('email', $username)->first();
    }

    protected function avatar(): Attribute
    {
        return Attribute::get(function ($avatar) {
            return match (true) {
                empty($avatar) => asset(sprintf('/images/avatar.png')),
                str_starts_with($avatar, 'http') => $avatar,
                default => asset(Storage::url($avatar)),
            };
        });
    }

    public function recalculatePoints(): void
    {
        $points = $this->events()
            ->where('points', '!=', 0)
            ->sum('points');

        $this->extends()->upsert(['points' => $points], ['user_id'], ['points']);
    }

    public function toSearchableArray(): array
    {
        return [
            'id'     => $this->getKey(),
            'name'   => $this->name,
            'avatar' => $this->avatar,
        ];
    }

    protected static function boot()
    {
        parent::boot();
        static::deleting(function (User $user) {
            $user->socialiteUsers()->delete();
        });
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }
}
