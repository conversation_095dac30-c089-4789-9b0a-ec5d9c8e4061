<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model as BaseModel;
use Illuminate\Support\Carbon;

/**
 * @mixin Builder
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class Model extends BaseModel
{
    const DELETED_AT = 'deleted_at';

    public static function getTableName(bool $full = false): string
    {
        $model     = new static;
        $tableName = $model->getTable();
        if ($full) {
            $tableName = $model->getConnectionName() . '.' . $tableName;
        }

        return $tableName;
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }
}
