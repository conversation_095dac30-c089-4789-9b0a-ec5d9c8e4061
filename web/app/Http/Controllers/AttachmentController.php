<?php

namespace App\Http\Controllers;

use App\Enum\ArticleFlag;
use App\Http\Requests\ArticleStoreRequest;
use App\Models\Article;
use App\Models\File;
use App\Models\User;
use App\Models\UserCheckIn;
use App\Utils\ViewRecorder\Recorder;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AttachmentController extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->middleware('auth')->only('create', 'store', 'edit', 'update', 'destroy');
    }

    /**
     * @throws AuthenticationException
     */
    public function download($id, Request $request)
    {
        if (!$user = $request->user()) {
            if ($request->hasValidSignature()) {
                $email = $request->query('email');
                $user  = User::query()->where('email', $email)->first();
            }
        }
        if (!$user) {
            throw new AuthenticationException();
        }

        /**
         * @var Article $article
         * @var File    $file
         */
        $file      = File::query()->where('uuid', $id)->firstOrFail();
        $extension = '.' . pathinfo($file->path, PATHINFO_EXTENSION);
        $name      = $file->name;
        if (!Str::endsWith($name, $extension)) {
            $name .= $extension;
        }
        return Response::download(Storage::disk('public')->path($file->path), $name, [
            'Content-Type' => 'application/octet-stream',
        ]);
    }
}
