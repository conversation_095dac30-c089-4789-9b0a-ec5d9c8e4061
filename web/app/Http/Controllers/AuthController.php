<?php

namespace App\Http\Controllers;

use App\Enum\LoginType;
use App\Enum\VerificationCodeType;
use App\Jobs\LogLogin;
use App\Models\LoginLog;
use App\Models\SocialiteUser;
use App\Models\User;
use App\Models\VerificationCode;
use App\Rules\Code;
use App\Rules\TCaptcha;
use App\Utils\DeviceDetector;
use App\Utils\Http\Api;
use App\Utils\Uploader;
use Exception;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;
use Laravel\Socialite\Facades\Socialite;
use Throwable;

class AuthController extends Controller
{
    const SOCIALITE_LOGIN_PARAM_KEY = 'socialite.login';

    public function getLogin(Request $request, DeviceDetector $device)
    {
        if (Auth::guest()) {
            return match (true) {
                $device->isWechatMiniProgram() => view('wechat.mini-program.login', ['url' => $request->fullUrl()]),
                //                $device->isWechat() => Response::redirectToRoute('socialite.login', ['driver' => 'wechat', 'redirect_uri' => $request->fullUrl()]),
                default => view('auth.login'),
            };
        }

        if ($request->query('client_id')) {
            return Response::redirectToRoute('authorizations.authorize', $request->query());
        }

        return Response::redirectTo($request->query('redirect_uri', '/'));
    }

    /**
     * @throws ValidationException
     * @throws Exception
     */
    public function postLogin(Request $request): JsonResponse
    {
        $this->validate($request, [
            'email'       => ['required', 'email', new TCaptcha()],
            'password'    => 'required',
            'remember_me' => 'boolean',
        ], [], __('models/user.attributes'));

        if (!Auth::attempt($request->only(['email', 'password']), (bool)$request->input('remember_me'))) {
            throw new Exception(__('auth.failed'));
        }

        $user = Auth::user();
//        LogLogin::dispatch($user = Auth::user(), $request, LoginType::Password);

        if ($user->isDisabled()) {
            abort(403, "用户被封禁，解禁日期为{$user->disabled_until->toDateTimeString()}");
        }

        return Api::okay([
            'access_token' => $user->createToken('api')->accessToken,
            'user'         => $user,
        ]);
    }

    /**
     * @throws ValidationException|Throwable
     */
    public function postEmailLogin(Request $request): JsonResponse
    {
        $input = $this->validate($request, [
            'email' => ['bail', 'required', 'email'],
            'code'  => ['required', new Code(VerificationCodeType::Email)],
        ], attributes: ['email' => '邮箱', 'code' => '验证码']);
        $email = $input['email'];
        $user  = DB::transaction(function () use ($email) {
            if (!$user = User::query()->where('email', $email)->lockForUpdate()->first()) {
                $user = User::query()->create([
                    'email'    => $email,
                    'nickname' => '邮箱用户' . Str::random(4),
                ]);
            }
            return $user;
        });

        if ($user->isDisabled()) {
            abort(403, "用户解禁日期为{$user->disabled_until->toDateTimeString()}");
        }

        Auth::login($user);
        //            event(new Registered($user));

        return Api::okay();
    }

    /**
     * @throws ValidationException
     */
    public function sendMailVerificationCode(Request $request)
    {
        $input = $this->validate($request, [
            'email' => ['required', 'email', new TCaptcha()],
        ], attributes: ['email' => '邮箱']);
        $code  = VerificationCode::generate(VerificationCodeType::Email, $email = $input['email']);
        Mail::to($email)->queue(new \App\Mail\VerificationCode($code->code));

        return Api::okay();
    }

    /**
     * @throws ValidationException
     */
    public function getVerificationCode(Request $request): HttpResponse|JsonResponse
    {
        switch ((int)$request->input('type')) {
            case VerificationCodeType::Email->value:
                $input = $this->validate($request, [
                    'email' => ['required', 'email', new TCaptcha()],
                ], attributes: ['email' => '邮箱']);
                $code  = VerificationCode::generate(VerificationCodeType::Email, $email = $input['email']);
                Mail::to($email)->queue(new \App\Mail\VerificationCode($code->code));
                break;
            default:
                throw new InvalidArgumentException('不支持的验证码类型');
        }

        return Api::okay();
    }

    /**
     * @throws ValidationException
     */
    public function verifyVerificationCode(Request $request)
    {
        $input = $this->validate($request, [
            'type' => ['required', Rule::enum(VerificationCodeType::class)],
            'code' => 'required',
        ]);
        /** @var VerificationCode $verificationCode */
        if (!$verificationCode = VerificationCode::available()->where($input)->first()) {
            throw new InvalidArgumentException('授权码不存在或者失效');
        }

        Auth::login($user = User::query()->findOrFail($verificationCode->identity));
        $verificationCode->delete();

        return Api::okay([
            'user'  => $user,
            'token' => $user->createToken('api')->accessToken,
        ]);
    }

    /**
     * 社会化登录入口
     */
    public function socialiteRedirect(string $driver, Request $request)
    {
        $request->session()->put(self::SOCIALITE_LOGIN_PARAM_KEY, $request->query('redirect_uri'));
        $request->session()->put('show_success', (bool)$request->query('show_success'));

        return Socialite::driver($driver)->redirect();
    }

    /**
     * 社会化登录回调
     *
     * @throws Throwable
     */
    public function socialiteCallback($driver, Request $request)
    {
        /**
         * @var SocialiteUser               $socialiteUser
         * @var \Laravel\Socialite\Two\User $sUser
         */
        $sUser = Socialite::driver($driver)->stateless()->user();
        Log::debug('社交账号登录', ['driver' => $driver, 'user' => (array)$sUser]);
        $socialiteUser = SocialiteUser::query()
            ->lockForUpdate()
            ->updateOrCreate([
                'provider' => SocialiteUser::PROVIDER[$driver],
                'identity' => $sUser->getId(),
            ], [
                'email'      => (string)$sUser->getEmail(),
                'nickname'   => (string)$sUser->getNickname(),
                'name'       => (string)$sUser->getName(),
                'avatar'     => (string)$sUser->getAvatar(),
                'attributes' => $sUser,
            ]);
        /*
         | --------------------------------------------------------
         | 未登录用户，使用绑定的账号或者创建新账号
         | --------------------------------------------------------
         */
        if (Auth::guest()) {
            if (!$user = $socialiteUser->user) {
                $user = DB::transaction(function () use ($socialiteUser, $sUser) {
                    return tap($socialiteUser->user()->create([
                        'nickname' => (string)$sUser->getNickname(),
                        'name'     => (string)$sUser->getName(),
                        'avatar'   => (string)$sUser->getAvatar(),
                    ]), function (User $user) use ($socialiteUser) {
                        $socialiteUser->update(['user_id' => $user->getKey()]);
                    });
                });
            }

            Auth::login($user);
//            LogLogin::dispatch($user, $request, LoginLog::LOGIN_TYPE[$driver]);
            if (DeviceDetector::isMobile()) {
                return Response::redirectTo($request->session()->pull(self::SOCIALITE_LOGIN_PARAM_KEY, route('login')));
            }

            return Response::view('auth.login-success');
        }

        $user = Auth::user();
        /*
         * 如果当前社交帐号有绑定账号, 且绑定的账号和当前账号一致，则直接跳转
         * 否则跳转到帐号绑定页面
         */
        if ($user->is($socialiteUser->user)) {
//            LogLogin::dispatch($user, $request, LoginLog::LOGIN_TYPE[$driver]);
            if (DeviceDetector::isMobile()) {
                return Response::redirectTo($request->session()->pull(self::SOCIALITE_LOGIN_PARAM_KEY, route('login')));
            }

            return Response::view('auth.login-success');
        }

        Auth::guard('socialite')->login($socialiteUser);
//        LogLogin::dispatch($user, $request, LoginLog::LOGIN_TYPE[$driver]);

        return Response::redirectToRoute('socialite.bind');
    }

    public function socialiteBind(Request $request)
    {
        // TODO 需要登录
        /**
         * @var User          $user
         * @var SocialiteUser $socialiteUser
         */
        $user          = $request->user();
        $socialiteUser = $request->user('socialite');
        if ($user->is($socialiteUser->user)) {
            return Response::redirectTo($request->session()->pull(self::SOCIALITE_LOGIN_PARAM_KEY, route('login')));
        }

        if ($action = $request->query('action')) {
            if ($action === 'skip') {
                if ($avatar = $socialiteUser->avatar) {
                    try {
                        $avatar = Uploader::fromUrl($avatar, 'png');
                    } catch (Exception $e) {
                        Log::error('下载头像失败，' . $e->getMessage(), ['url' => $avatar]);
                    }
                }
                $user = DB::transaction(function () use ($socialiteUser, $avatar) {
                    $user = User::query()->create([
                        'name'     => $socialiteUser->name,
                        'nickname' => $socialiteUser->nickname,
                        'avatar'   => $avatar,
                    ]);
                    $socialiteUser->update(['user_id' => $user->getKey()]);
                    return $user;
                });
                Auth::login($user);
                Auth::guard('socialite')->logout();

                return $request->session()->pull('show_success') ? view('auth.bind-success') : Response::redirectToRoute('login');
            }

            if ($action === 'confirm') {
                if (!$user = Auth::user()) {
                    throw new Exception('账号未登录');
                }
                try {
                    return DB::transaction(function () use ($request, $user, $socialiteUser) {
                        /** @var SocialiteUser $oldSUser */
                        if (!$oldSUser = SocialiteUser::query()->lockForUpdate()->find($socialiteUser->id)) {
                            throw new Exception('社交账号已经被注销，不能操作');
                        }
                        if (!empty($oldSUser->user_id)) {
                            throw new Exception($oldSUser !== $user->id ? '该账号已经被绑定' : '您已绑定该账号，无需重复绑定');
                        }
                        $socialiteUser->update(['user_id' => $user->id]);
                        Auth::guard('socialite')->logout();
                        return $request->session()->pull('show_success') ? view('auth.bind-success') : Response::redirectToRoute('login');
                    });
                } catch (Exception $e) {
                    return back()->withErrors(['bind' => $e->getMessage()]);
                }
            }
        }

        return view('auth.bind', ['user' => $user, 'socialiteUser' => $socialiteUser]);
    }

    /**
     * @throws Throwable
     * @throws ValidationException
     */
    public function socialiteBindApprove(Request $request)
    {
        $data = $this->validate($request, [
            'email'    => ['required', 'email'],
            'password' => 'required',
        ]);
        /** @var SocialiteUser $socialiteUser */
        if (!$socialiteUser = Auth::guard('socialite')->user()) {
            return Response::redirectToRoute('login');
        }
        try {
            $user = DB::transaction(function () use ($data, $socialiteUser) {
                /** @var User $user */
                if (!$user = User::query()->where('email', $data['email'])->first()) {
                    throw new Exception('用户信息不存在');
                }
                if (!Hash::check($data['password'], $user->password)) {
                    throw new Exception('密码不正确');
                }
                if ($user->socialiteUsers()->where('provider', $socialiteUser->provider)->lockForUpdate()->exists()) {
                    throw new Exception($socialiteUser->user_id === $user->id ? '账号已经绑定，不需要再次绑定' : '当前社交账号已经绑定其他账户');
                }

                $socialiteUser->update(['user_id' => $user->id]);

                return $user;
            });

            Auth::login($user);
            Auth::guard('socialite')->logout();

            return $request->session()->pull('show_success') ? view('auth.bind-success') : Response::redirectToRoute('login');
        } catch (Exception $e) {
            return back()->withErrors(['bind' => $e->getMessage()]);
        }
    }

    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        if ($redirectURI = $request->query('redirect_uri')) {
            return Response::redirectTo($redirectURI);
        }

        return Response::redirectToIntended();
    }

    /**
     * 验证邮箱
     */
    public function verifyEmail(EmailVerificationRequest $request): RedirectResponse
    {
        $request->fulfill();

        return Response::redirectToRoute('login');
    }

    /**
     * @throws ValidationException
     */
    public function changePassword(Request $request): JsonResponse
    {
        $input          = $this->validate($request, ['password' => 'required|confirmed']);
        $originPassword = $request->input('origin_password');
        /** @var User $user */
        $user = $request->user();
        if ($user->password && !Hash::check($originPassword, $user->password)) {
            throw new \InvalidArgumentException('原密码不正确');
        }
        $user->forceFill(['password' => Hash::make($input['password'])])->save();
        event(new PasswordReset($user));
        return Api::okay();
    }
}
