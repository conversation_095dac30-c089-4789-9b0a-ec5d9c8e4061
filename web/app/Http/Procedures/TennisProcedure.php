<?php

declare(strict_types=1);

namespace App\Http\Procedures;

use Illuminate\Http\Request;
use Sajya\Server\Procedure;

class TennisProcedure extends Procedure
{
    /**
     * The name of the procedure that is used for referencing.
     *
     * @var string
     */
    public static string $name = 'TennisProcedure';

    /**
     * Execute the procedure.
     *
     * @param Request $request
     *
     * @return mixed
     */
    public function handle(Request $request)
    {
        // write your code
    }

    /**
     * Execute the procedure.
     */
    public function ping()
    {
//        throw new \Exception('ping error');
        return [
            'pong'
        ];
    }
}
