<?php

namespace App\Console\Commands;

use App\Models\Sticker;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateStickerOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-sticker-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新sticker排序';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Sticker::query()->update(['order' => DB::raw('rand() * 999999')]);
    }
}
