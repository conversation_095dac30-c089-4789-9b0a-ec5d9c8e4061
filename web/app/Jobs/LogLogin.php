<?php

namespace App\Jobs;

use App\Enum\LoginType;
use App\Models\LoginLog;
use Carbon\Carbon;
use DeviceDetector\ClientHints;
use DeviceDetector\DeviceDetector;
use DeviceDetector\Parser\Device\AbstractDeviceParser;
use Exception;
use GuzzleHttp\RequestOptions;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class LogLogin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string  $userId;
    protected ?string $ip;
    protected ?string $userAgent;
    protected array   $serverParams;
    protected string  $now;

    public function __construct(
        Authenticatable     $user,
        Request             $request,
        protected LoginType $loginType = LoginType::Password
    )
    {
        $this->userId       = $user->getAuthIdentifier();
        $this->ip           = $request->ip();
        $this->userAgent    = $request->userAgent();
        $this->serverParams = $request->server->all();
        $this->now          = Carbon::now()->toDateTimeString();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        LoginLog::unguarded(function () {
            AbstractDeviceParser::setVersionTruncation(AbstractDeviceParser::VERSION_TRUNCATION_NONE);
            $clientHints = ClientHints::factory($_SERVER); // client hints are optional
            $dd          = new DeviceDetector($this->userAgent, $clientHints);
            $dd->parse();
            $device = '';
            if (($osName = $dd->getOs('name')) && ($osVersion = $dd->getOs('version'))) {
                $device .= $osName . ' ' . $osVersion;
            }
            if (($clientName = $dd->getClient('name')) && ($clientVersion = $dd->getClient('version'))) {
                if (!empty($device)) {
                    $device .= ' ';
                }
                $device .= $clientName . ' ' . $clientVersion;
            }

            LoginLog::query()->create([
                'user_id'    => $this->userId,
                'login_type' => $this->loginType,
                'ip'         => $this->ip,
                'ua'         => $this->userAgent,
                'device'     => $device,
                'location'   => $this->ip2location(),
                'created_at' => $this->now,
            ]);
        });
    }

    public function ip2location()
    {
        try {
            $response = Http::withOptions([
                RequestOptions::TIMEOUT => 5,
                RequestOptions::VERIFY  => false,
            ])->get('https://apis.map.qq.com/ws/location/v1/ip', [
                'ip'  => $this->ip,
                'key' => 'UZOBZ-R2U3V-7TFPT-5BZ6D-VOTQ3-FKB55',
            ]);

            $data = json_decode($response->body(), true);
            if (isset($data['status'])) {
                if ($data['status'] !== 0) {
                    return $data['status'] === 375 ? $data['message'] : null;
                }
                $location = $data['result']['ad_info'];
                if (isset($location['nation'], $location['province'], $location['city'])) {
                    return implode(' ', array_filter([$location['nation'], $location['province'], $location['city']]));
                }
            }

            return null;
        } catch (Exception) {
            return null;
        }
    }
}
