<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property User $user
 */
class UserEvent extends Model
{
    use SoftDeletes, HasUuids;

    protected $table    = 'user_events';
    protected $fillable = [
        'user_id',
        'event_id',
        'points',
        'scene',
        'description',
        'resource',
        'details',
    ];
    protected $casts    = [
        'points'  => 'decimal:2',
        'details' => 'array',
    ];

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }

    protected static function boot()
    {
        parent::boot();
        static::created(function (UserEvent $userEvent) {
            $userEvent->user?->recalculatePoints();
        });
    }
}
