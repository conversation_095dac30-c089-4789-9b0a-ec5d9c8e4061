<?php

namespace App\Providers;

use App\Admin\Repositories\Navigation\Config as ConfigRepositories;
use App\Models\Client;
use App\Models\User;
use App\Observers\UserObserver;
use App\Utils\DfaFilter\SensitiveHelper;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Passport;
use Spatie\Translatable\Facades\Translatable;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        Json::encodeUsing(function (mixed $value) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        });
        Passport::ignoreRoutes();
        Passport::tokensExpireIn(now()->addDays(10));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::useClientModel(Client::class);
        Passport::setDefaultScope(['base_info']);
        Passport::enablePasswordGrant();
        Passport::tokensCan([
            'base_info' => '获取您的基本用户信息（头像，昵称等）',
        ]);

        Translatable::fallback('zh-CN');

        $this->app->singleton(SensitiveHelper::class, function () {
            $filter = new SensitiveHelper();
            $filter->setTreeByFile(storage_path('words.txt'));
            return $filter;
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (Schema::hasTable('navigation_configs')) {
            ConfigRepositories::load();
        }
        Relation::enforceMorphMap([
            'user'         => 'App\Models\User',
            'article'      => 'App\Models\Article',
            'event'        => 'App\Models\Activity',
            'sticker'      => 'App\Models\Sticker',
            'sticker-pack' => 'App\Models\StickerPack',
        ]);

        User::observe(UserObserver::class);
    }
}
