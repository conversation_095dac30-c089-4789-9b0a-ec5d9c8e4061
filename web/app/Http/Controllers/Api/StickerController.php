<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\StickerCollection;
use App\Http\Resources\StickerResource;
use App\Jobs\UserEventDispatcher;
use App\Models\Category;
use App\Models\Event;
use App\Models\SearchLog;
use App\Models\Sticker;
use App\Models\StickerPack;
use App\Utils\Http\Api;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StickerController extends Controller
{
    public function __construct()
    {
        $this->middleware('api.protection')->only(['index', 'favorite', 'feedback']);
        $this->middleware('auth:api')->except(['index', 'show']);
        parent::__construct();
    }

    public function index(Request $request)
    {
        $stickers = Sticker::with('pack:id,uuid,name,image')
            ->when($request->query('keyword'), function (Builder $query, $keyword) {
                SearchLog::query()->create([
                    'query'   => $keyword,
                    'user_id' => (int)Auth::id(),
                ]);
                $query->where('name', 'like', sprintf('%%%s%%', $keyword));
            })
            ->when($request, function (Builder|Category $query, Request $request) {
                if ($categoryId = $request->get('category_id')) {
                    $query->whereHas('categories', fn(Builder $query) => $query->enabled()->where('uuid', $categoryId));
                } else if (($parentCategoryId = $request->get('parent_category_id')) &&
                    $category = Category::query()->where('uuid', $parentCategoryId)->first()
                ) {
                    $query->whereHas('categories', fn(Builder $query) => $query->enabled()
                        ->where(fn($query) => $query->where('parent_id', $category->getKey())
                            ->orWhere('id', $category->getKey())));
                }
            })
            ->when($request->input('pack_id'), function (Builder $query, $packId) {
                if ($stickerPack = StickerPack::query()->where('uuid', $packId)->first()) {
                    $query->where('pack_id', $stickerPack->getKey());
                }
            })
            ->orderByRaw('rand()')
            ->orderBy('views', 'DESC')
            ->simplePaginate(page_size_resolver(21, 21)());

        return Api::okay(new StickerCollection($stickers));
    }

    public function show($id, Request $request)
    {
        $sticker = Sticker::with(['pack:id,uuid,name,image', 'categories:id,uuid,name'])
            ->where('uuid', $id)
            ->firstOrFail();

        $sticker['favorited'] = ($user = $request->user()) &&
            $user->favoritedStickers()->find($sticker->getKey());;

        $sticker->timestamps = false;
        $sticker->increment('views');

        return Api::okay(new StickerResource($sticker));
    }

    public function favorite($id, Request $request)
    {
        $sticker = Sticker::query()->where('uuid', $id)->firstOrFail();
        $user    = $request->user();
        if ($request->isMethod('DELETE')) {
            $user->favoritedStickers()->detach($sticker);
        } else {
            try {
                $user->favoritedStickers()->attach($sticker);
                UserEventDispatcher::dispatch(
                    $user,
                    Event::SLUG_STICKER_FAVORITE,
                    $sticker->getKey(),
                    triggeredAt: now(),
                    details: [
                        'model' => Sticker::class,
                        'id'    => $sticker->getKey(),
                    ],
                );
            } catch (Exception) {
            }
        }

        return Api::okay();
    }

    public function feedback($id, Request $request)
    {
        $this->validate($request, [
            'feedback' => 'required|max:200',
        ]);

        $user     = $request->user();
        $feedback = Sticker::query()
            ->where('uuid', $id)
            ->firstOrFail()
            ->feedbacks()
            ->create([
                'feedback' => $request->input('feedback'),
                'user_id'  => (int)$user?->getKey(),
            ]);

        return Api::okay($feedback);
    }
}
