<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\FileUpdateRequest;
use App\Models\File;
use App\Models\User;
use App\Utils\Http\Api;
use App\Utils\Uploader;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManager;
use InvalidArgumentException;
use RuntimeException;
use Throwable;

class FileController extends Controller
{
    public function index(Request $request)
    {
        $parentId = (string)$request->input('parent_id');
        /** @var User $user */
        $user  = $request->user();
        $files = $user->files()
                      ->where('parent_id', $parentId)
                      ->when($request->query('keyword'), function (Builder $query, $kw) {
                          $query->where('name', 'like', "%{$kw}%");
                      })
                      ->latest('is_dir')
                      ->latest()
                      ->paginate(page_size_resolver());

        return Api::okay($files);
    }

    public function update($fileId, FileUpdateRequest $request)
    {
        $this->user
            ->files()
            ->where('uuid', $fileId)
            ->firstOrFail()
            ->update($request->validated());


        return Api::okay();
    }

    public function store(Request $request)
    {
        $input = $this->validate($request, [
            'hash'      => 'string',
            'name'      => 'string',
            'parent_id' => 'uuid',
            'type'      => [Rule::in(File::TYPE)],
            'file'      => ['file', 'max:' . 20 * 1024 * 1024, Rule::requiredIf(empty($request->input('hash')))], // 大小20MB
        ], [
            'file.max' => '文件大小不能超过20MB',
        ]);

        /**
         * @var UploadedFile $file
         * @var User         $user
         * @var File         $file
         */
        $user = $request->user();
        if ($hash = $request->input('hash')) {
            if (!$file = File::withTrashed()->where('hash', $hash)->first()) {
                return Api::fail("文件不存在", silent: true);
            }
            $attrs              = $file->toArray();
            $attrs['type']      = $input['type'] ?? File::TYPE['picture'];
            $attrs['name']      = Str::random(32);
            $attrs['parent_id'] = $input['parent_id'] ?? "";
            $file               = $user->files()->create($attrs);
        } else {
            /** @var UploadedFile $uploadedFile */
            $uploadedFile = $input['file'];
            $hash         = strtoupper(hash_file('sha256', $uploadedFile->getRealPath()));
            $file         = File::withTrashed()->where('hash', $hash)->first();
            if ($file) {
                $attrs              = $file->toArray();
                $attrs['type']      = $input['type'] ?? File::TYPE['picture'];
                $attrs['name']      = Str::random(32);
                $attrs['parent_id'] = $input['parent_id'] ?? "";
                $file               = $user->files()->create($attrs);
            } else {
                if (Str::startsWith($uploadedFile->getMimeType(), 'image')) {
                    if (!is_dir($realpath = Storage::disk('public')->path($relativePath = date('Ym/d')))) {
                        mkdir($realpath, 0755, true);
                    }
                    $filename = Str::random(32) . '.webp';
                    ImageManager::gd()->read($uploadedFile->getRealPath())->save($realpath . '/' . $filename);
                    $filepath = $relativePath . '/' . $filename;
                } else {
                    $filepath = $uploadedFile->store(date('Ym/d'), ['disk' => 'public']);
                }
                $file = $user->files()->create([
                    'path'      => $filepath,
                    'size'      => $uploadedFile->getSize(),
                    'mime_type' => $uploadedFile->getMimeType(),
                    'hash'      => $hash,
                    'parent_id' => $input['parent_id'] ?? '',
                    'type'      => $input['type'] ?? File::TYPE['picture'],
                    'name'      => $input['name'] ?? pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME),
                ]);
            }
        }

        return Api::okay($file);
    }

    /**
     * @throws ValidationException
     */
    public function import(Request $request)
    {
        $url = $this->validate($request, ['url' => 'required|url'])['url'];
        try {
            $client = new Client();
            $ext    = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            [, $fullPath, $filename] = Uploader::newFile($ext, 'tmp');
            $response = $client->get($url, [RequestOptions::SINK => $fullPath . $filename]);
            $stream   = $response->getBody();
            $uri      = $stream->getMetadata('uri');
            $mimeType = (string)mime_content_type($uri);
            if (!str_starts_with($mimeType, 'image')) {
                throw new RuntimeException('仅支持导入图片格式文件');
            }
            $hash = strtoupper(hash_file('sha256', $uri));
            /** @var User $user */
            $user = $request->user();
            if ($file = File::withTrashed()->where('hash', $hash)->first()) {
                $attrs = $file->toArray();
            } else {
                [$newRelativePath, $newFullPath, $newFilename] = Uploader::newFile($ext, disk: 'public');
                rename($fullPath . $filename, $newFullPath . $newFilename);
                $attrs = [
                    'path'      => $newRelativePath . $newFilename,
                    'size'      => filesize($newFullPath),
                    'mime_type' => $mimeType,
                ];
            }
            $attrs['name'] = pathinfo($url, PATHINFO_BASENAME);
            $attrs['hash'] = $hash;

            return Api::okay($user->files()->create($attrs));
        } catch (GuzzleException $e) {
            throw new RuntimeException('下载失败', previous: $e);
        }
    }

    /**
     * @throws Throwable
     */
    public function destroy($id, Request $request)
    {
        $file = $request->user()->files()->where('uuid', $id)->firstOrFail();
        DB::transaction(function () use ($file) {
            $uuids = [$file->uuid];
            do {
                $query = File::query()->whereIn('uuid', $uuids);
                $uuids = $query->pluck('uuid');
                $query->delete();
                $uuids = File::query()->whereIn('parent_id', $uuids->toArray())->pluck('uuid');
            } while ($uuids->isNotEmpty());
        });

        return Api::okay();
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     */
    public function search(Request $request)
    {
        $this->validate($request, ['source' => Rule::in(['pixels', 'pixabay']),]);
        $client  = new Client();
        $keyword = $request->query('keyword');
        $page    = $request->query('page', 1);
        $perPage = $request->query('pageSize', 10);
        if ($request->query('source', 'pixels') === 'pixels') {
            $response = $client->get('https://api.pexels.com/v1/search', [
                RequestOptions::HEADERS => [
                    'Authorization' => '63GomXvH6rfmUTeN45TqadzTsa9jA3XFCbnkctCRuX6PzXNwWGd00TkP',
                ],
                RequestOptions::QUERY   => [
                    'query'    => $keyword,
                    'page'     => $page,
                    'per_page' => $perPage,
                    'locale'   => 'zh-CN',
                ],
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            if (!is_array($result) || !isset($result['total_results'])) {
                throw new Exception('搜索失败，请重试');
            }
            $items = array_map(function ($item) {
                return [
                    'id'   => $item['id'],
                    'name' => $item['alt'],
                    'url'  => $item['src']['large'] ?? $item['src']['original'],
                ];
            }, $result['photos']);

            $result = new LengthAwarePaginator($items, $result['total_results'], $result['per_page'], $result['page']);
        } else {
            $response = $client->get('https://pixabay.com/api/', [
                RequestOptions::QUERY => [
                    'key'      => '44125135-8809d0f3a1370e01d1f621c9d',
                    'q'        => $keyword,
                    'page'     => $page,
                    'per_page' => $perPage,
                    'lang'     => 'zh',
                ],
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            if (!is_array($result) || !isset($result['totalHits'])) {
                throw new Exception('搜索失败，请重试');
            }

            $items = array_map(function ($item) {
                return [
                    'id'   => $item['id'],
                    'name' => $item['tags'],
                    'url'  => $item['webformatURL'],
                ];
            }, $result['hits']);

            $result = new LengthAwarePaginator($items, $result['totalHits'], $perPage, $page);
        }

        return Api::okay($result);
    }

    public function addFolder(Request $request)
    {
        $this->validate($request, ['name' => 'required', 'parent_id' => 'uuid']);
        /** @var User $user */
        $user     = $request->user();
        $name     = $request->input('name');
        $parentId = $request->input('parent_id', "");
        if ($user->files()->where('name', $name)->where('parent_id', $parentId)->exists()) {
            throw new InvalidArgumentException('已经存在同名分组了');
        }

        $user->files()
             ->newModelInstance()
             ->forceFill([
                 'name'      => $name,
                 'is_dir'    => true,
                 'parent_id' => $parentId,
                 'user_id'   => $user->getKey(),
             ])
             ->save();

        return Api::okay();
    }
}
