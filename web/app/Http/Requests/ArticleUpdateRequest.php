<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ArticleUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'title'    => 'sometimes|required|max:60',
            'abstract' => 'max:300',
            'content'  => 'string|nullable',
            'cover'    => 'string|nullable',
            'tag_id'   => 'array',
            'tag_id.*' => 'exists:tags,id',
            'featured' => 'boolean',
            'draft'    => 'boolean',
            'flag'     => 'integer',
        ];
    }

    public function attributes()
    {
        return __('models/user.attributes');
    }
}
