<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\Date;
use App\Admin\Repositories\Sticker;
use App\Admin\Repositories\StickerPack;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class StickerPackController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    public function grid()
    {
        return Grid::make(new StickerPack(), function (Grid $grid) {
            $grid->model()->orderByDesc('created_at');
            $grid->column('image')->image(width: 100, height: 100);
            $grid->column('name')->width(300);
            $grid->column('created_at')->displayUsing(Date::class)->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式

            // 添加跳转到sticker页面的按钮
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<div class="btn-group" style="margin-right: 5px">
                    <a href="' . admin_url('stickers') . '" class="btn btn-mini btn-primary" title="跳转到表情页面">
                        <i class="feather icon-image"></i><span class="d-none d-sm-inline">&nbsp;表情管理</span>
                    </a>
                </div>');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new StickerPack(), function (Show $show) {
            $show->field('name');
            $show->field('image')->image();
            $show->field('created_at');
            $show->field('updated_at');
            $show->relation('stickers', function (\App\Models\StickerPack $stickerPack) {
                $grid = new Grid(new Sticker());
                $grid->model()->where('pack_id', $stickerPack->getKey());

                // 设置路由
                $grid->setResource('stickers');

                $grid->column('name');
                $grid->column('image')->image();
                $grid->created_at();
                $grid->updated_at();

                return $grid;
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new StickerPack(), function (Form $form) {
            $form->ignore(['image_type']);
            $form->text('name');
            $form->radio('image_type')
                ->value(1)
                ->when(1, function (Form $form) {
                    $form->image('image')->required();
                })
                ->when(2, function (Form $form) {
                    $form->text('image')->required();
                })
                ->options([1 => '图片上传', 2 => '图片链接']);

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
