<?php

namespace App\Admin\Forms;

use App\Models\Sticker;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class StickerPack extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        Sticker::query()->where('id', $this->payload['key'])
            ->update($input);

        return $this->response()
            ->success('保存成功')
            ->refresh();
    }

    public function form()
    {
        $this->select('pack_id')
            ->options(\App\Models\StickerPack::query()->pluck('name', 'id'))
            ->value($this->payload['pack_id'])
            ->required();
    }
}
