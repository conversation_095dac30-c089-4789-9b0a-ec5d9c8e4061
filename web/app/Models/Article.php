<?php

namespace App\Models;

use App\Utils\Eloquent\Concern\HasFavoritedBy;
use App\Utils\ViewRecorder\HasViews;
use App\Utils\ViewRecorder\ShouldRecordViews;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Laravel\Scout\Searchable;

/**
 * @property int                                $id
 * @property string                             $uuid
 * @property string                             $user_id
 * @property string                             $title     标题
 * @property string                             $abstract  摘要
 * @property string                             $content   内容
 * @property string                             $cover
 * @property bool                               $draft     是否是草稿
 * @property bool                               $featured  是否精选的
 * @property int                                $flag      一些标识
 * @property int                                $views
 * @property User                               $user
 * @property User                               $author
 * @property Collection|iterable<int, Category> $categories
 * @property Collection|iterable<int, Comment>  $comments
 * @property Collection|iterable<int, Tag>      $tags
 * @method static Builder|static featured() 精选范围
 * @method static Builder|static visible() 可见范围
 */
class Article extends Model implements ShouldRecordViews
{
    use SoftDeletes, HasUuids;
    use HasViews;
    use HasFavoritedBy;

//    use Searchable;

    protected $hidden   = [self::DELETED_AT, 'user_id', 'pivot'];
    protected $fillable = [
        'user_id',
        'title',
        'abstract',
        'content',
        'cover',
        'order',
        'draft',
        'featured',
        'flag',
        'views',
    ];
    protected $casts    = [
        'flag'     => 'integer',
        'order'    => 'integer',
        'draft'    => 'boolean',
        'featured' => 'boolean',
    ];

    /**
     * @return BelongsTo
     * @deprecated 使用author
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    protected function content(): Attribute
    {
        return Attribute::get(fn($value) => (string)$value);
    }

    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'model', 'model_has_categories', parentKey: 'uuid', relatedKey: 'uuid');
    }

    public function files(): MorphToMany
    {
        return $this->morphToMany(File::class, 'model', 'model_has_files', parentKey: 'id', relatedKey: 'id');
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class, 'article_id', 'uuid');
    }

    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'model', 'model_has_tags', 'tag_id', 'id', 'id', 'id')
            ->withTimestamps();
    }

    /**
     * 精选作用范围
     */
    protected function scopeFeatured(Builder $query): void
    {
        $query->where('featured', true);
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }

    /**
     * 可见，草稿不显示
     */
    protected function scopeVisible(Builder $query): void
    {
        $query->where('draft', false);
    }

    public function shouldBeSearchable(): bool
    {
        return !is_null($this->author);
    }

    public function toSearchableArray(): array
    {
        return [
            ...Arr::except($this->attributesToArray(), 'content'),
            'tags'   => $this->tags->transform(fn(Tag $tag) => $tag->toSearchableArray()),
            'author' => $this->author->toSearchableArray(),
            'cover'  => url(Storage::url($this->cover)),
        ];
    }
}
