<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserCheckIn extends Model
{
    use HasFactory;

    protected $table = 'user_checkins';
    protected $fillable = ['user_id', 'checkin_at', 'ranking'];
    protected $hidden = ['id', 'user_id'];
    protected $casts = ['checkin_at' => 'datetime'];

    const CREATED_AT = null;
    const UPDATED_AT = null;

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
