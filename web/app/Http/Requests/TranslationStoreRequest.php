<?php

namespace App\Http\Requests;

use App\Models\Translation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TranslationStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'key'   => [
                'required',
                'string',
                'max:191',
                Rule::unique('translations', 'key')->whereNull(Translation::DELETED_AT)
            ],
            'zh-CN' => 'string|max:191',
            'en'    => 'string|max:191',
        ];
    }
}
