<?php

namespace App\Faker;

use Faker\Provider\Base;

class CustomNicknameProvider extends Base
{
    public function nickname()
    {
        $skills = [
            '做饭', '编程', '画画', '唱歌', '跳舞', '写诗', '种花', '修电脑', '飞',
            '讲故事', '弹吉他', '做手工', '玩游戏', '煮咖啡', '拍照片',
            '写代码', '做甜点', '养宠物', '看星星', '听音乐', '跑步'
        ];

        $subjects = [
            '小猫咪', '程序员', '艺术家', '旅行者', '吃货', '梦想家',
            '冒险家', '书呆子', '夜猫子', '阳光少年', '小仙女', '老司机',
            '追梦人', '孤独者', '好奇宝宝', '瞌睡虫', '行动派', '思考者',
            '快乐星球', '彩虹糖', '闪电侠', '风行者', '星辰'
        ];

        // 随机选择各部分并组合成"会做xx的xxx"格式
        return '会' . $skills[array_rand($skills)] . '的' . $subjects[array_rand($subjects)];
    }
}
