<?php

namespace App\Http\Controllers\Api\WeChat;

use App\Enum\VerificationCodeType;
use App\Enum\WechatAppType;
use App\Http\Controllers\Controller;
use App\Models\SocialiteUser;
use App\Models\User;
use App\Models\VerificationCode;
use App\Models\WechatApp;
use EasyWeChat\Kernel\Exceptions\BadRequestException;
use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
use EasyWeChat\Kernel\Exceptions\RuntimeException;
use EasyWeChat\OfficialAccount\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\Http\Message\ResponseInterface;

class OfficialAccountController extends Controller
{
    /**
     * @throws InvalidArgumentException
     * @throws BadRequestException
     * @throws \Throwable
     * @throws \ReflectionException
     * @throws RuntimeException
     */
    public function handle($appId): ResponseInterface
    {
        $app    = WechatApp::query()
            ->where('type', WechatAppType::OfficialAccount)
            ->where('app_id', $appId)
            ->firstOrFail();
        $server = $app->getService()->getServer();
        $server->addEventListener('subscribe', function (Message $message, \Closure $next) {
            /** @var SocialiteUser $socialiteUser */
            $socialiteUser = SocialiteUser::query()->with(['user'])->firstOrCreate([
                'provider' => SocialiteUser::PROVIDER['wechat'],
                'identity' => $message->FromUserName,
            ]);
            if (!$socialiteUser->user) {
                $user = $socialiteUser->user()->create(['nickname' => '微信用户' . Str::random(4)]);
                $socialiteUser->update(['user_id' => $user->getKey()]);
            }

            return '感谢关注！';
        })->with(function (Message $message, \Closure $next) {
            Log::info('message', [$message]);
            /**
             * @var User          $user
             * @var SocialiteUser $socialiteUser
             */
            $socialiteUser = SocialiteUser::query()->with(['user'])->firstOrCreate([
                'provider' => SocialiteUser::PROVIDER['wechat'],
                'identity' => $message->FromUserName,
            ]);
            if (!$user = $socialiteUser->user) {
                $user = $socialiteUser->user()->create(['nickname' => '微信用户' . Str::random(4)]);
                $socialiteUser->update(['user_id' => $user->getKey()]);
            }

            switch ($message->Content) {
                case '验证码':
                    $verificationCode = VerificationCode::generate(VerificationCodeType::WeChat, $user->getKey());
                    return sprintf('您的验证码是【%s】。验证码%d分钟后过期，使用后失效！', $verificationCode->code, VerificationCode::TTL / 60);
            }

            return $next($message);
        });

        return $server->serve();
    }
}
