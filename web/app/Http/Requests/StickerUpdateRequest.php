<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StickerUpdateRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name'         => 'string|max:255',
            'image'        => 'sometimes|required|string|max:255',
            'categories'   => 'array',
            'categories.*' => 'exists:categories,id',
        ];
    }
}
