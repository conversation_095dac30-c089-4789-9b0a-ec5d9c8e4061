<?php

namespace App\Http\Controllers\Api\WeChat;

use App\Exceptions\BusinessException;
use App\Http\Controllers\Controller;
use App\Models\SocialiteUser;
use App\Models\SystemLog;
use App\Models\User;
use App\Models\WechatApp;
use App\Utils\Http\Api;
use App\Utils\Util;
use EasyWeChat\Kernel\Exceptions\HttpException;
use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Throwable;

class MiniProgramController extends Controller
{
    /**
     * @throws InvalidArgumentException
     * @throws ValidationException
     * @throws HttpException
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Throwable
     */
    public function login($appId, Request $request): JsonResponse
    {
        /** @var User $user */
        $inputs = $this->validate($request, ['code' => 'required']);
        if (!$wechatApp = WechatApp::miniProgram()->where('app_id', $appId)->first()) {
            throw new BusinessException('未找到小程序应用');
        }
        $service = $wechatApp->getService();
        $session = $service->getUtils()->codeToSession($inputs['code']);
        // todo 创建用户和wechat app对应关系
        Log::info('session', $session);
        $user = DB::transaction(function () use ($service, $session, $appId) {
            /** @var SocialiteUser $socialiteUser */
            $socialiteUser = SocialiteUser::query()->firstOrCreate([
                'provider'     => SocialiteUser::PROVIDER['wechat_miniprogram'],
                'app_id'       => $appId,
                'identity_key' => 'openid',
                'identity'     => $session['openid'],
            ]);
            if (!$user = $socialiteUser->user) {
                $user = $socialiteUser->user()->create(['nickname' => Util::faker()->nickname]);
                $socialiteUser->update(['user_id' => $user->getKey()]);
            }

            return $user;
        });

        if (($systemInfo = $request->input('system_info')) &&
            $systemInfo = json_decode($systemInfo, true)
        ) {
            $user->extends()->updateOrCreate([], ['system_info' => $systemInfo]);
        }

        return Api::okay([
            'user'  => $user,
            'token' => $user->createToken('api')->accessToken,
        ]);
    }

    public function reportSystemError($appid, Request $request)
    {
        if ($error = $request->input('error')) {
            SystemLog::query()->create([
                'app_id'  => $appid,
                'log'     => $error,
                'user_id' => (int)Auth::id(),
            ]);
        }

        return Api::okay();
    }
}
