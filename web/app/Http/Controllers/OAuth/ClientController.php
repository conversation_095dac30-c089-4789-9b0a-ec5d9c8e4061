<?php

namespace App\Http\Controllers\OAuth;

use App\Http\Resources\ClientGrantedUsersCollection;
use App\Http\Resources\ClientResource;
use App\Models\Client;
use App\Utils\Http\Api;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Passport\Http\Controllers\ClientController as PassportClientController;
use Laravel\Passport\Passport;
use Throwable;

class ClientController extends PassportClientController
{
    public function forUser(Request $request)
    {
        return Api::okay(ClientResource::collection(parent::forUser($request)));
    }

    public function store(Request $request)
    {
        $this->validation->make($request->all(), [
            'name' => 'required|max:191',
            'redirect' => ['required', $this->redirectRule],
            'confidential' => 'boolean',
        ])->validate();

        $client = $this->clients->create(
            $request->user()->getAuthIdentifier(), $request->name, $request->redirect,
            (string)$request->query('provider'), false, false, (bool)$request->input('confidential', true)
        );

        if (Passport::$hashesClientSecrets) {
            return ['plainSecret' => $client->plainSecret] + $client->toArray();
        }

        return Api::okay($client->makeVisible('secret'));
    }

    /**
     * @throws Throwable
     */
    public function destroy(Request $request, $clientId)
    {
        Parent::destroy($request, $clientId);

        return Api::okay();
    }

    public function show($clientId, Request $request)
    {
        $userId = $request->user()->getAuthIdentifier();

        /** @var Client $client */
        if (!($client = $this->clients->findForUser($clientId, $userId)) || $client->revoked) {
            abort(404);
        }

        return Api::okay(new ClientResource($client));
    }

    public function grantedUsers($clientId, Request $request)
    {
        $userId = $request->user()->getAuthIdentifier();

        /** @var Client $client */
        if (!($client = $this->clients->findForUser($clientId, $userId)) || $client->revoked) {
            abort(404);
        }

        return Api::okay(new ClientGrantedUsersCollection($client->grantedUsers()->paginate()));
    }
}
