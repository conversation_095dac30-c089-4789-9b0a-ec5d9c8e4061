<?php

namespace App\Console\Commands;

use App\Models\Article;
use Elastic\Client\ClientBuilder;
use Illuminate\Console\Command;

class EsMigrate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:migrate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
//        foreach ([Article::class] as $model) {
//            app(ClientBuilder::class)->default()->indices()->putMapping([
//                'index' => with(new $model)->searchableAs(),
//                'body'  => [
//                    'properties' => [
//                        'created_at' => [
//                            'type'      => 'text',
//                            'fielddata' => true,
//                        ],
//                        'updated_at' => [
//                            'type'      => 'text',
//                            'fielddata' => true,
//                        ],
//                        'tags'       => [
//                            'type'       => 'nested',
//                            'properties' => [
//                                'id'   => ['type' => 'integer'],
//                                'name' => ['type' => 'keyword'],
//                                'icon' => ['type' => 'text'],
//                            ],
//                        ],
//                    ],
//                ],
//            ]);
//        }

        $this->call('scout:import', ['model' => 'App\\Models\\Article']);
    }
}
