<?php

namespace App\Admin\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $queries = $this->validate($request, [
            'q' => 'required',
        ]);

        return User::query()
            ->where('nickname', 'like', "%{$queries['q']}%")
            ->paginate(null, DB::raw('id,nickname as text'));
    }
}
