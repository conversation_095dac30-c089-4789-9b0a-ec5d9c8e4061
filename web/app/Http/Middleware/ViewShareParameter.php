<?php

namespace App\Http\Middleware;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;

class ViewShareParameter
{
    public function handle(Request $request, \Closure $next)
    {
        if ($request->query->has('token')) {
            if ($request->query->get('token')) {
                $user = Auth::guard('api')->user();
                Auth::login($user);
            } else {
                Auth::logout();
            }
        }
        
        /** @var User $user */
        $user = $request->user();
        View::share('user', $user);
        View::share('notificationsCount', (int)$user?->notifications()->whereNull('read_at')->count());
        $supportLanguages = [
            'zh-CN' => [
                'name' => '简体中文',
                'icon' => '/images/lang/zh-CN.svg',
            ],
            'en'    => [
                'name' => 'English',
                'icon' => '/images/lang/en.svg',
            ],
        ];
        if (!isset($supportLanguages[$locale = App::getLocale()])) {
            $locale = App::getFallbackLocale();
        }
        App::setLocale($locale);
        View::share('languages', $supportLanguages);

        $menus = [
            [
                'label' => trans('pages/main.home'),
                'path'  => '/',
            ],
            [
                'label' => trans('pages/main.article'),
                'path'  => '/articles',
            ],
            [
                'label' => '直播',
                'path'  => '/event',
            ]
        ];
        View::share('menus', $menus);
        View::share('title', '小禾纸条');
        View::share('accessToken', (string)$user?->createToken('api')->accessToken);

        return $next($request);
    }
}
