<?php

namespace App\Admin\Repositories;

use App\Models\Category as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Category extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function getCategories($root = true)
    {
        $categories = $this->model()->get();
        $parent     = $this->formatCategory($this->array2level($categories));

        if ($root) {
            $default = [0 => 'ROOT'];
            if (count($parent) > 0) {
                $parent = $default + $parent;
            } else {
                $parent = $default;
            }
        }

        return $parent;
    }

    /**
     * 数组层级缩进转换
     *
     * @param array $array 源数组
     * @param int   $pid
     * @param int   $level
     *
     * @return array
     */
    function array2level($array, $pid = 0, $level = 1)
    {
        static $list = [];
        foreach ($array as $v) {
            if ($v['parent_id'] == $pid) {
                $v['level'] = $level;
                $list[]     = $v;
                $this->array2level($array, $v['id'], $level + 1);
            }
        }

        return $list;
    }

    /**
     * 格式化分类
     */
    function formatCategory($array)
    {
        foreach ($array as &$v) {
            $levelStr = '';

            for ($i = 1; $i < $v['level']; $i++) {
                $levelStr = '&nbsp;&nbsp;&nbsp;&nbsp;';
            }

            $v['name'] = $levelStr . $v['name'];
        }
        unset($v);

        return array_column($array, 'name', 'id');
    }

}
