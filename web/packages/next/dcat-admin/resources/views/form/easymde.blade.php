<style>
    .CodeMirror.CodeMirror-fullscreen,
        /* 为同时包含editor-toolbar和fullscreen类的元素设置z-index */
    .editor-toolbar.fullscreen {
        z-index: 9999;
    }

    .editor-toolbar > button {
        width: unset;
    }
</style>
<div class="{{$viewClass['form-group']}} {{ $class }}">
    <label class="{{$viewClass['label']}} control-label">{!! $label !!}</label>
    <div class="{{$viewClass['field']}}">
        @include('admin::form.error')
        <textarea class="form-control" id="{{$id}}" name="{{$name}}"
                  placeholder="{{ $placeholder }}" {!! $attributes !!}>{!! $value !!}</textarea>
        @include('admin::form.help-block')
    </div>
</div>

<script require="@easymde" init="{!! $selector !!}">
    const element = document.getElementById('{{$id}}');
    const easyMDE = new EasyMDE({
        element: element,
        uploadImage: true,
        imageUploadEndpoint: "/admin/api/upload/images",
        ...{!! $options !!}
    });
    // 监听编辑器内容变化事件
    easyMDE.codemirror.on('change', () => {
        element.value = easyMDE.value();
    });
</script>
