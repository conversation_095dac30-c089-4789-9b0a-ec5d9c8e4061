<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\AvatarWithName;
use App\Admin\Repositories\Todo;
use App\Enum\TodoStatus;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class TodoController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Todo(), function (Grid $grid) {
            $grid->model()->latest();
            $grid->column('uuid');
            $grid->column('user')->displayUsing(AvatarWithName::class);
            $grid->column('title');
            $grid->column('description')->textarea()->width(200);
            $grid->column('status')->badge([
                TodoStatus::Pending->value   => 'yellow',
                TodoStatus::Completed->value => 'green',
            ]);
            $grid->column('completed_at')->sortable();
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Todo(), function (Show $show) {
            $show->field('id');
            $show->field('uuid');
            $show->field('user_id');
            $show->field('title');
            $show->field('description');
            $show->field('status');
            $show->field('completed_at');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Todo(), function (Form $form) {
            $form->number('user_id');
            $form->text('title')->required();
            $form->textarea('description');
            $form->select('status')->options(trans('todo.options.status'))->required();
            $form->datetime('completed_at');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
