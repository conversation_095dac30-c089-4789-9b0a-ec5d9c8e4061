<?php

namespace App\Admin\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category as CategoryModel;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $parentId = $request->get('q');
        if ($parentId) {
            return CategoryModel::query()
                ->where('parent_id', $parentId)
                ->get(DB::raw('id,name as text'));
        }
        return CategoryModel::query()
            ->where('parent_id', '!=', 0)
            ->get(DB::raw('id,name as text'));
    }
}
