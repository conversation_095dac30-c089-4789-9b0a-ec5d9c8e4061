<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserStoreRequest;
use App\Http\Requests\UserUpdateRequest;
use App\Http\Resources\LoginLogCollection;
use App\Http\Resources\UserClientGrantCollection;
use App\Models\SocialiteUser;
use App\Models\User;
use App\Utils\Http\Api;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use OpenApi\Attributes as OA;

class UserController extends Controller
{
    public function index(Request $request)
    {
        return Api::okay(
            User::query()
                ->when($request->query('nickname'), function (Builder $query, $nickname) {
                    $query->where('nickname', 'like', "%$nickname%");
                })
                ->latest()
                ->paginate($request->query('pageSize'))
        );
    }

    public function show(User $user)
    {
        return Api::okay($user->load('loginLogs'));
    }

    public function store(UserStoreRequest $request)
    {
        return Api::okay(User::query()->create($request->validated()));
    }

    public function update(User $user, UserUpdateRequest $request)
    {
        $user->update($request->validated());

        return Api::okay();
    }

    public function destroy(User $user)
    {
        $user->delete();

        return Api::okay();
    }

    public function getLoginLogs(User $user)
    {
        return Api::okay(new LoginLogCollection($user->loginLogs()->latest()->paginate(page_size_resolver())));
    }

    public function unbindSocialite($provider): JsonResponse
    {
        if (!isset(SocialiteUser::PROVIDER[$provider])) {
            throw new \InvalidArgumentException('参数错误');
        }
        Auth::user()
            ->socialiteUsers()
            ->where('provider', SocialiteUser::PROVIDER[$provider])
            ->delete();

        return Api::okay();
    }

    #[OA\Get(
        path: '/api/v1/user/socialite-users',
        summary: '获取用户社交账号信息',
        security: [['api' => []]],
        tags: ['用户'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'ok',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'code', description: '错误码', type: 'int'),
                        new OA\Property(property: 'data', description: '数据', type: 'array', items: new OA\Items(ref: SocialiteUser::class)),
                    ],
                )
            ),
        ]
    )]
    public function socialiteUsers(Request $request): JsonResponse
    {
        return Api::okay($request->user()->socialiteUsers);
    }


    public function socialites(Request $request): JsonResponse
    {
        $user       = $request->user();
        $socialites = $user->socialiteUsers;
        $result     = [];
        foreach (SocialiteUser::PROVIDER as $key => $provider) {
            $result[] = [
                'provider'  => $key,
                'logo'      => asset(sprintf('/images/%s.png', $key)),
                'name'      => __('models/socialiteUser.provider.' . $provider),
                'socialite' => $socialites->where('provider', $provider)->first(),
            ];
        }

        return Api::okay($result);
    }

    public function loginLogs(Request $request)
    {
        return Api::okay(new LoginLogCollection($request->user()->loginLogs()->latest()->paginate(5)));
    }

    public function clientGrants(Request $request)
    {
        /** @var User $user */
        $user = $request->user();
        return Api::okay(new UserClientGrantCollection($user->clientGrants));
    }
}

