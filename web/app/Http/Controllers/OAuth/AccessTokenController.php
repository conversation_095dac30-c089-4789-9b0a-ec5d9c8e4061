<?php

namespace App\Http\Controllers\OAuth;

use <PERSON><PERSON>\Passport\Http\Controllers\AccessTokenController as PassportAccessTokenController;
use OpenApi\Attributes as OA;
use Psr\Http\Message\ServerRequestInterface;

class AccessTokenController extends PassportAccessTokenController
{
    #[OA\Post(
        path: '/oauth/token',
        summary: '获取token',
        requestBody: new OA\RequestBody(content: new OA\JsonContent(properties: [
            new OA\Property(property: 'grant_type', description: '授权类型', enum: ['authorization_code']),
            new OA\Property(property: 'client_id', description: '客户端id'),
            new OA\Property(property: 'client_secret', description: '客户端secret'),
            new OA\Property(property: 'redirect_uri', description: '回调地址'),
            new OA\Property(property: 'client_code', description: 'code'),
            new OA\Property(property: 'scope', description: '范围'),
        ])),
        tags: ['OAuth'],
        responses: [
            new OA\Response(response: 200, description: 'ok', content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'code', description: '错误码'),
                    new OA\Property(property: 'data', description: '数据', properties: [
                        new OA\Property(property: 'refresh_token', description: '刷新令牌'),
                        new OA\Property(property: 'access_token', description: 'access令牌'),
                    ]),
                ],
            )),
        ],
    )]
    public function issueToken(ServerRequestInterface $request)
    {
        return parent::issueToken($request);
    }
}
