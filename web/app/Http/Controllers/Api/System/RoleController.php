<?php

namespace App\Http\Controllers\Api\System;

use App\Utils\Http\Api;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Throwable;

class RoleController extends Controller
{
    public function index()
    {
        return Api::okay(Role::query()->paginate());
    }

    public function store(Request $request)
    {
        $input = $this->validate($request, [
            'name' => 'required',
        ]);

        return Api::okay(Role::query()->create($input));
    }

    public function show(Role $role)
    {
        return Api::okay($role->load('permissions'));
    }

    /**
     * @throws Throwable
     */
    public function destroy(Role $role)
    {
        DB::transaction(function () use ($role) {
            $role->permissions()->detach();
            $role->delete();
        });

        return Api::okay();
    }

    public function givePermissions(Role $role, Request $request)
    {
        $inputs = $request->validate([
            'permissions'   => 'required|array',
            'permissions.*' => 'string|exists:permissions,name'
        ]);

        $role->givePermissionTo($inputs['permissions']);

        return Api::okay();
    }
}
