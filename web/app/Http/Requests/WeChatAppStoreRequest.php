<?php

namespace App\Http\Requests;

use App\Enum\WechatAppType;
use App\Models\WechatApp;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WeChatAppStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|max:20',
            'type' => ['required', Rule::enum(WechatAppType::class)]
        ];
    }
}
