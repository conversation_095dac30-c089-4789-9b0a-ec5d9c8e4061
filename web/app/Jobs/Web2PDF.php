<?php

namespace App\Jobs;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use InvalidArgumentException;
use App\Utils\TraceableJob;

class Web2PDF extends TraceableJob
{
    /**
     * @throws Exception
     */
    protected function run(): array
    {
        $payload = $this->payload;
        if (!isset($payload['url'])) {
            throw new InvalidArgumentException('url is required');
        }
        $savePath = Storage::path(Str::random(32) . '.pdf');
        $cmd      = vsprintf('%s %s %s %s 2>&1', [
            'node',
            escapeshellarg(base_path('pdf.js')),
            escapeshellarg($payload['url']),
            escapeshellarg($savePath),
        ]);
        $result   = Process::run($cmd)->output();
        Log::info('Web2PDF', ['cmd' => $cmd, 'result' => $result]);
        if (!file_exists($savePath)) {
            throw new Exception('内部错误');
        }
        return ['url' => $savePath];
    }
}
