<?php

namespace App\Models;

use App\Enum\VerificationCodeType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * @property Carbon $expires_at
 * @property string $code
 * @property string $tries
 * @property string $identity
 * @method static Builder|static available(bool $available = true)// 是否可用
 */
class VerificationCode extends Model
{
    use HasFactory, Prunable;

    /**
     * 有效期
     */
    const TTL  = 60 * 5;

    public    $timestamps = false;
    protected $fillable   = ['type', 'identity', 'code', 'sent_at', 'expires_at'];
    protected $casts      = [
        'type'       => VerificationCodeType::class,
        'sent_at'    => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * 生成新的验证码，旧的过期
     */
    public static function generate(VerificationCodeType $type, string $identity, int $length = 6): static
    {
        return DB::transaction(function () use ($type, $identity, $length) {
            self::where(['type' => $type, 'identity' => $identity])->delete();
            return self::create([
                'type'       => $type,
                'identity'   => $identity,
                'sent_at'    => now(),
                'expires_at' => now()->addSeconds(self::TTL),
                'code'       => strtoupper(Str::random($length)),
            ]);
        });
    }

    /**
     * 获取可修剪模型查询构造器。
     */
    public function prunable(): Builder
    {
        return static::available(false);
    }

    /**
     * 获取是否可用的授权码
     */
    protected function scopeAvailable($query, bool $available = true): void
    {
        $query->where('expires_at', $available ? '>=' : '<', Carbon::now());
    }
}
