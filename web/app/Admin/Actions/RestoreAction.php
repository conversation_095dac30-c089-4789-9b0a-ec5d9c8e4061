<?php

namespace App\Admin\Actions;

use Dcat\Admin\Grid\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RestoreAction extends RowAction
{
    public $name = '恢复';

    /**
     * @param Model&SoftDeletes $model
     *
     * @return \Dcat\Admin\Actions\Response
     */
    public function handle(Model $model)
    {
        $model->restore();
        return $this->response()->success('恢复成功')->refresh();
    }

    public function dialog()
    {
        $this->confirm('确定要恢复这条记录吗？');
    }
}
