<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\WeChatAppStoreRequest;
use App\Http\Requests\WeChatAppUpdateRequest;
use App\Utils\Http\Api;
use Illuminate\Http\Request;

class WeChatAppController extends Controller
{
    public function index(Request $request)
    {
        $weChatApps = $this->user->weChatApps()
            ->when($request->get('type'), fn($query, $type) => $query->where('type', $type))
            ->when($request->get('name'), fn($query, $name) => $query->where('name', 'like', "%$name%"))
            ->paginate(page_size_resolver(100));

        return Api::okay($weChatApps);
    }

    public function store(WeChatAppStoreRequest $request)
    {
        return Api::okay($this->user->weChatApps()->create($request->validated()));
    }

    public function update($id, WeChatAppUpdateRequest $request)
    {
        $this->user->weChatApps()
            ->where('id', $id)
            ->update($request->validated());

        return Api::okay();
    }

    public function destroy($id)
    {
        $this->user->weChatApps()
            ->where('id', $id)
            ->delete();

        return Api::okay();
    }
}
