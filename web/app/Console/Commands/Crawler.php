<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\Sticker;
use App\Models\StickerPack;
use App\Models\Tag;
use GuzzleHttp\RequestOptions;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;


class Crawler extends Command
{
    protected $signature = 'crawler';

    protected $ignored = [
        'https://mmbiz.qpic.cn/mmbiz_jpg/NXOpeoH53b8ICInsYhascia1kTicFarTjhiaJMcW1nWoZGJ9DSZ2TDatgEtJpf9W90Hb0wGV8cv2GnFvPLZw9PwAw/0?wx_fmt=jpeg',
    ];

    public function handle()
    {
//        $this->avatar();
//        $this->wallpaper();
        $this->other();
//        $this->emote();
//        $this->special();
    }

    protected function avatar()
    {
        $page = 0;
        while (true) {
            try {
                $page++;
                $url = 'https://img.shixiaoyang.com/app/index.php?i=6&t=0&v=1.0.0&from=wxapp&c=entry&a=wxapp&do=r_imglist&m=xz_bq&sign=8e9daeabee261abd908997faa17c3c50&page=' . $page . '&type=3&imgid=17139&classid=0&r=500252';
                dump($url);
                $res = Http::withOptions([
                    RequestOptions::VERIFY => false,
                ])->get($url);
                $res = json_decode((string)$res->getBody(), true);
                if ($data = Arr::get($res, 'data')) {
                    dump($data);
                    foreach ($data as $item) {
                        $image    = str_replace('http://', 'https://', (string)Arr::get($item, 'opath'));
                        $category = Category::query()->firstOrCreate([
                            'parent_id' => 23,
                            'name'      => $item['name'],
                        ]);
                        $pack     = StickerPack::query()->firstOrCreate([
                            'name' => $item['name'],
                        ]);
                        $sticker  = Sticker::query()->updateOrCreate([
                            'image' => $image,
                        ], [
                            'user_id' => 0,
                            'pack_id' => $pack->getKey(),
                        ]);
                        $sticker->categories()->sync($category);
                    }
                } else {
                    break;
                }
            } catch (\Exception $e) {
                dump($e->getMessage());
                continue;
            }
        }
    }

    public function other()
    {
        $res      = Http::withOptions([
            RequestOptions::VERIFY => false,
        ])
            ->get('https://a.biaoqing.site/storage/background/random?per_page=1000');
//            ->get('https://a.biaoqing.site/storage/avatar/random?&per_page=1000');
        $res      = json_decode((string)$res->getBody(), true);
        $stickers = Arr::get($res, 'data.data');
        foreach ($stickers as $sticker) {
            $imageUrl = str_replace('http://', 'https://', (string)Arr::get($sticker, 'imgurl'));
            if (in_array($imageUrl, $this->ignored)) {
                continue;
            }

            /** @var Sticker $sticker */
            $sticker = Sticker::query()->firstOrCreate([
                'image' => $imageUrl,
            ], [
                'user_id' => 0,
            ]);
            $sticker->categories()->sync([28]);
        }
    }

    public function special()
    {
        $res      = Http::withOptions([
            RequestOptions::VERIFY => false,
        ])->get('https://a.biaoqing.site/storage/special/random?per_page=1000');
        $res      = json_decode((string)$res->getBody(), true);
        $stickers = Arr::get($res, 'data.data');
        foreach ($stickers as $sticker) {
            $images   = Http::withOptions([
                RequestOptions::VERIFY => false,
            ])->get(Arr::get($sticker, 'imgurl'));
            $images   = json_decode((string)$images->getBody(), true);
            $category = null;
            if ($name = Arr::get($sticker, 'title')) {
                $category = Category::query()->firstOrCreate(['name' => $name]);
            }
            foreach ($images as $image) {
                $imageUrl = str_replace('http://', 'https://', $image['comid']);
                if (in_array($imageUrl, $this->ignored)) {
                    continue;
                }
                if (Sticker::query()->where('image', $imageUrl)->exists()) {
                    continue;
                }
                /** @var Sticker $sticker */
                $sticker = Sticker::query()->create([
                    'user_id' => '',
                    'image'   => $imageUrl,
                ]);
                if ($category) {
                    $sticker->categories()->sync($category);
                }
            }
        }
    }

    protected function emote()
    {
        for ($i = 1; $i < 3; $i++) {
            $res      = Http::get('https://a.biaoqing.site/storage/emote/random?per_page=10000&page=' . $i);
            $res      = json_decode((string)$res->getBody(), true);
            $stickers = Arr::get($res, 'data.data');
            foreach ($stickers as $sticker) {
                $imageUrl = str_replace('http://', 'https://', (string)Arr::get($sticker, 'imgurl'));
                if (in_array($imageUrl, $this->ignored)) {
                    continue;
                }
                if (Sticker::query()->where('image', $imageUrl)->exists()) {
                    continue;
                }

                /** @var Sticker $sticker */
                $sticker    = Sticker::query()->create([
                    'user_id' => '',
                    'name'    => (string)Arr::get($sticker, 'keyword'),
                    'image'   => $imageUrl,
                ]);
                $categories = collect();
                $cats       = array_filter([
                    Arr::get($sticker, 'category'),
                    Arr::get($sticker, 'category_real'),
                    Arr::get($sticker, 'classification'),
                ]);
                foreach ($cats as $cat) {
                    $categories->push(Category::query()->firstOrCreate(['name' => $cat]));
                }
                $tags = collect();
                $ts   = array_filter([
                    Arr::get($sticker, 'keyword'),
                    Arr::get($sticker, 'keyword2'),
                    Arr::get($sticker, 'keyword3'),
                ]);
                foreach ($ts as $t) {
                    $tags->push(Tag::query()->firstOrCreate(['name' => $t]));
                }
                $sticker->categories()->attach($categories);
                $sticker->tags()->attach($tags);
            }
        }
    }

    protected function wallpaper()
    {
        $res      = Http::withOptions([
            RequestOptions::VERIFY => false
        ])
            ->get('https://a.biaoqing.site/storage/wallpaper/random?per_page=1000');
        $res      = json_decode((string)$res->getBody(), true);
        $stickers = Arr::get($res, 'data.data');
        foreach ($stickers as $sticker) {
            $imageUrl = str_replace('http://', 'https://', (string)Arr::get($sticker, 'imgurl'));
            if (in_array($imageUrl, $this->ignored)) {
                continue;
            }
            /** @var Sticker $sticker */
            $sticker = Sticker::query()->firstOrCreate([
                'image' => $imageUrl,
            ], [
                'user_id' => 0,
                'name'    => (string)Arr::get($sticker, 'Categorize'),
            ]);
            $sticker->categories()->sync([28]);
        }
    }
}
