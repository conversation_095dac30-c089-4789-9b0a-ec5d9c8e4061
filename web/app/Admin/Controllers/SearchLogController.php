<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\AvatarWithName;
use App\Admin\Repositories\SearchLog;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class SearchLogController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SearchLog(['user']), function (Grid $grid) {
            $grid->model()->orderByDesc('created_at');
            $grid->column('user')->displayUsing(AvatarWithName::class);
            $grid->column('query');
            $grid->column('created_at')->sortable();
            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->disableCreateButton();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.nickname', '用户昵称');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SearchLog(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('query');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SearchLog(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('query');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
