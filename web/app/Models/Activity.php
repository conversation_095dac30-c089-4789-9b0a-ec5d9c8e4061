<?php

namespace App\Models;

use App\Utils\ViewRecorder\HasViews;
use App\Utils\ViewRecorder\ShouldRecordViews;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property string                             $cover
 * @property int                                $upvote
 * @property int                                $state
 * @property int                                $views
 * @property EventRegistrationForm              $registrationForm
 * @property Collection|EventRegistrationForm[] $registrationForms
 */
class Activity extends Model implements ShouldRecordViews
{
    use HasFactory, SoftDeletes;
    use HasViews;
    use HasUlids;

    protected $fillable = ['title', 'intro', 'start_time', 'real_start_time', 'end_time', 'user_id', 'state', 'cover', 'stream_push_token', 'upvotes', 'views'];
    protected $casts    = [
        'upvotes'         => 'integer',
        'views'           => 'integer',
        'state'           => 'integer',
        'start_time'      => 'datetime',
        'real_start_time' => 'datetime',
        'end_time'        => 'datetime',
    ];

    const STATE = [
        'ready'  => 1,
        'living' => 2,
        'closed' => 3,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function isState(int $state): bool
    {
        return $this->state === $state;
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }

    public function registrationForms(): HasMany
    {
        return $this->hasMany(EventRegistrationForm::class, 'event_id');
    }

    public function registrations(): HasManyThrough
    {
        return $this->hasManyThrough(EventRegistration::class, EventRegistrationForm::class, 'event_id', 'form_id');
    }
}
