<?php

namespace App\Http\Controllers\Api;

use App\Enum\ApiCode;
use App\Enum\TodoStatus;
use App\Exceptions\TodoException;
use App\Http\Controllers\Controller;
use App\Http\Requests\TodoStoreRequest;
use App\Http\Requests\TodoUpdateRequest;
use App\Models\Todo;
use App\Models\User;
use App\Utils\Http\Api;
use Illuminate\Http\Request;

class TodoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return Api::okay($request->user()->todos()->orderBy('status')->get());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TodoStoreRequest $request)
    {
        $inputs = $request->validated();
        /** @var User $user */
        $user       = $request->user();
        $todoExists = $user->todos()->where('title', $inputs['title'])->exists();
        if ($todoExists && !$request->query('confirm')) {
            throw new TodoException('已经存在同名的TODO，请确认是否要创建', ApiCode::TodoRepeatCreate->value);
        }
        $todo = $user->todos()->create($inputs);

        return Api::okay($todo);
    }

    /**
     * Display the specified resource.
     */
    public function show(Todo $todo)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Todo $todo)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TodoUpdateRequest $request, $todoId)
    {
        $todo    = $request->user()->todos()->where('uuid', $todoId)->firstOrFail();
        $updates = $request->validated();
        if (isset($updates['status'])) {
            $updates['completed_at'] = $updates['status'] == TodoStatus::Completed->value ? now() : null;
        }
        $todo->update($updates);

        return Api::okay();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($todoId, Request $request)
    {
        $request->user()->todos()->where('uuid', $todoId)->delete();

        return Api::okay();
    }
}
