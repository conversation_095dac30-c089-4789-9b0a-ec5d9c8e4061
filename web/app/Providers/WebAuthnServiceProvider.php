<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class WebAuthnServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // In a real implementation with the package, this would register
        // the WebAuthn routes, views, and other components

        // For now, we're just creating the structure
    }
}
