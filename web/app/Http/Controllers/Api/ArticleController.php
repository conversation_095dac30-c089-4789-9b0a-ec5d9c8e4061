<?php

namespace App\Http\Controllers\Api;

use App\Enum\ArticleFlag;
use App\Http\Controllers\Controller;
use App\Http\Requests\ArticleStoreRequest;
use App\Http\Requests\ArticleUpdateRequest;
use App\Http\Resources\ArticleCollection;
use App\Http\Resources\ArticleResource;
use App\Models\Article;
use App\Models\User;
use App\Utils\Http\Api;
use Exception;
use Fig\Http\Message\StatusCodeInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;

class ArticleController extends Controller
{
    public function __construct()
    {
        $this->middleware('api.protection')->only('index');
        $this->middleware('auth:api')->except(['index', 'show']);
        parent::__construct();
    }

    public function index(Request $request)
    {
        $this->validate($request, [
            'featured' => 'boolean',
            'excludes' => 'string',
        ]);
        $featured = $request->query('featured');
        $articles = Article::visible()
            ->with(['tags:id,name,icon', 'author:id,nickname,avatar'])
            ->when($request->query('query'), fn($query, $keyword) => $query->where('title', 'like', sprintf('%%%s%%', $keyword)))
            ->when(!is_null($featured), fn($query) => $query->where('featured', (bool)$featured))
            ->when($request->input('excludes'), fn($query, $ids) => $query->whereNotIn('uuid', str2array($ids)))
            ->latest('order')
            ->latest()
            ->paginate(page_size_resolver(), columns: ['uuid', 'user_id', 'title', 'cover', 'abstract', Article::CREATED_AT]);

        return Api::okay(new ArticleCollection($articles));
    }

    public function show($id, Request $request): JsonResponse
    {
        $article = Article::query()
            ->with(['author'])
            ->where('uuid', $id)
            ->firstOrFail();

        $favorited = false;
        if ($user = $request->user()) {
            $favorited = (bool)$user->favoritedArticles()->find($article->getKey());
        }

        $article['favorited'] = $favorited;

//        Queue::push(new LogViews($article, request(), auth()->user()));
        if (!Auth::guard('api')->user() && ($article->flag & ArticleFlag::needLoginWhenRead->value) !== 0) {
            $article->makeHidden(['content', 'files']);
        }

        $article->timestamps = false;
        $article->increment('views');

        return Api::okay(new ArticleResource($article));
    }

    /**
     * @throws Throwable
     */
    public function store(ArticleStoreRequest $request): JsonResponse
    {
        $data    = $request->validated();
        $article = DB::transaction(function () use ($data, $request) {
            /** @var Article $article */
            $article = $request->user()->articles()->create($data);
            if (isset($data['tags'])) {
                $article->tags()->attach($data['tags']);
            }
            return $article;
        });

        return Api::okay($article, StatusCodeInterface::STATUS_CREATED);
    }

    /**
     * @throws Throwable
     */
    public function update($id, ArticleUpdateRequest $request): JsonResponse
    {
        /** @var Article $article */
        $article = $request->user()->articles()->where('uuid', $id)->firstOrFail();
        $data    = $request->validated();
        DB::transaction(function () use ($article, $data) {
            $article->update($data);
            if (isset($data['tags'])) {
                $article->tags()->sync($data['tags']);
            }
        });

        return Api::okay();
    }

    /**
     * 删除文章
     *
     * @throws Throwable
     */
    public function destroy($id, Request $request): JsonResponse
    {
        /**
         * @var User    $user
         * @var Article $article
         */
        $user    = $request->user();
        $article = $user->articles()->where('uuid', $id)->firstOrFail();
        DB::transaction(function () use ($article) {
            $article->delete();
            $article->tags()->detach();
            $article->comments()->delete();
        });

        return Api::okay();
    }

    public function favorite($id, Request $request)
    {
        $sticker = Article::query()->where('uuid', $id)->firstOrFail();
        $user    = $request->user();
        if ($request->isMethod('DELETE')) {
            $user->favoritedArticles()->detach($sticker);
        } else {
            try {
                $user->favoritedArticles()->attach($sticker);
            } catch (Exception) {
            }
        }

        return Api::okay();
    }
}
