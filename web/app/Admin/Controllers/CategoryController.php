<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\Date;
use App\Admin\Renderable\StickerTable;
use App\Admin\Repositories\Category;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Tree;

class CategoryController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
//            ->body($this->treeView());
    }

    protected function treeView()
    {
        return new Tree(Category::with('children'), function (Tree $tree) {
            $tree->disableCreateButton();
            $tree->disableEditButton();

            $tree->branch(function ($branch) {
                $branchName = htmlspecialchars($branch->name);
                $src        = $branch['icon'] ?? '';
                $statusText = $branch->enabled ? '<span style="color: dodgerblue">开启</span>' : '<span style="color: red">关闭</span>';
                $logo       = "<i class='iconfont $src' style='max-width:20px;max-height:20px'/>";
                return "$logo {$branchName} | 当前状态：{$statusText}";
            });
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(\App\Models\Category::query()->withCount(['stickers']), function (Grid $grid) {
            $grid->column('name')->tree();
            $grid->column('uuid');
            $grid->column('order')->orderable();
            $grid->column('icon');
            $grid->column('slug');
            $grid->column('stickers_count')->modal('贴纸列表', StickerTable::make());
            $grid->column('enabled')->switch();
            $grid->column('description');
            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });

            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->disableBatchDelete();
            $grid->disableViewButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Category(), function (Show $show) {
            $show->field('id');
            $show->field('uuid');
            $show->field('name');
            $show->field('slug');
            $show->field('description');
            $show->field('icon');
            $show->field('order');
            $show->field('parent_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Category(), function (Form $form) {
            $form->select('parent_id')->options(app(Category::class)->getCategories(false))->default(0);
            $form->text('name');
            $form->text('slug');
            $form->textarea('description');
            $form->text('icon');
            $form->number('order');
            $form->switch('enabled');
        });
    }
}
