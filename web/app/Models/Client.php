<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use <PERSON><PERSON>\Passport\Client as BaseClient;

/**
 * @property string  $id
 * @property string  $name
 * @property string  $logo
 * @property boolean $skips_authorization
 * @property boolean $revoked
 */
class Client extends BaseClient
{
    protected $casts    = [
        'grant_types'            => 'array',
        'personal_access_client' => 'bool',
        'password_client'        => 'bool',
        'revoked'                => 'bool',
        'skips_authorization'    => 'bool',
    ];
    protected $fillable = [
        'id',
        'user_id',
        'name',
        'description',
        'logo',
        'secret',
        'redirect',
        'personal_access_client',
        'password_client',
        'revoked',
        'skips_authorization',
    ];
    protected $hidden   = ['pivot',];

    /**
     * @inheritDoc
     */
    public function skipsAuthorization(): bool
    {
        return $this->firstParty() || ($this->attributes['skips_authorization'] ?? false);
    }

    public function grantedUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, (new UserClientGrant())->getTable(), 'client_id', 'user_id')
            ->withTimestamps();
    }
}
