<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\FailedJob;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class FailedJobController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new FailedJob(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('uuid');
            $grid->column('connection');
            $grid->column('queue');
            $grid->column('payload');
            $grid->column('exception');
            $grid->column('failed_at');
            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->disableCreateButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new FailedJob(), function (Show $show) {
            $show->field('id');
            $show->field('uuid');
            $show->field('connection');
            $show->field('queue');
            $show->field('payload');
            $show->field('exception');
            $show->field('failed_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new FailedJob(), function (Form $form) {
            $form->display('id');
            $form->text('uuid');
            $form->text('connection');
            $form->text('queue');
            $form->text('payload');
            $form->text('exception');
            $form->text('failed_at');
        });
    }
}
