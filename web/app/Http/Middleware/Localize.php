<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class Localize
{
    const LOCALE_NAME = 'lang';

    const ACCEPT_LOCALES = ['en', 'zh-CN'];

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request                                                                          $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if ($locale = $request->query(self::LOCALE_NAME)) {
            $locale = $this->getLegalLocale($locale);
            App::setLocale($locale);
            $response = $next($request);
            if ($request->wantsJson()) {
                return $response;
            }
            return $response->cookie(self::LOCALE_NAME, $locale);
        }

        if ($locale = $request->cookie(self::LOCALE_NAME)) {
            App::setLocale($this->getLegalLocale($locale));
        }

        return $next($request);
    }

    protected function getLegalLocale($locale)
    {
        if (!in_array($locale, self::ACCEPT_LOCALES)) {
            $locale = \app()->getLocale();
        }

        return $locale;
    }
}
