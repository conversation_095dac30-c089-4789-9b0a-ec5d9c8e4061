<?php

namespace App\Http\Middleware;

use App\Models\WechatApp;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class EnhancedApiProtectionMiddleware
{
    protected function isTrialVersion(Request $request): bool
    {
        return $request->input('version') !== 'release';
    }

    /**
     * 处理传入的请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (app()->isLocal()) {
            return $next($request);
        }

        if (!($appId = $request->input('app_id')) ||
            !WechatApp::miniProgram()->where('app_id', $appId)->exists()
        ) {
            throw new \Exception('app不存在');
        }

        // 1. UA验证 - 仅允许微信小程序访问
        if (!$this->validateUserAgent($request)) {
            return $this->blockRequest('非法客户端');
        }

        // 2. 频率限制 - 每分钟最多100次请求
        if ($this->tooManyRequests($request)) {
            return $this->blockRequest('请求频率过高', 429);
        }

        // 3. 签名验证 - 确保请求来自可信客户端
        if (!$this->validateSignature($request)) {
            return $this->blockRequest('无效签名');
        }

        // 4. 设备指纹验证 - 防止同一设备频繁更换身份
//        if (!$this->validateDevice($request)) {
//            return $this->blockRequest('可疑设备');
//        }

        // 5. 验证码验证（针对敏感接口）
//        if ($this->needsCaptcha($request) && !$this->validateCaptcha($request)) {
//            return $this->blockRequest('验证码错误');
//        }

        // 所有检查通过，继续处理请求
        return $next($request);
    }

    /**
     * 验证User-Agent
     */
    private function validateUserAgent(Request $request): bool
    {
        $userAgent = $request->header('User-Agent');
        return $userAgent && (
                str_contains($userAgent, 'miniProgram') ||
                str_contains($userAgent, 'MicroMessenger')
            );
    }

    /**
     * 频率限制检查
     */
    private function tooManyRequests(Request $request): bool
    {
        $key = 'api-throttle:' . $request->ip();
        return RateLimiter::tooManyAttempts($key, 60);
    }

    /**
     * 签名验证
     */
    private function validateSignature(Request $request): bool
    {
        // 检查请求是否包含签名
        if (!$request->has('timestamp') || !$request->has('signature')) {
            return false;
        }

        $signature = $request->input('signature');
        $cacheKey  = 'api-signature:' . $signature;
        if (Cache::has($cacheKey)) {
            return false;
        }

        $ttl       = 300;
        $timestamp = $request->input('timestamp');
        if ((int)(microtime(true) * 1000) - $timestamp > $ttl * 1000) {
            return false;
        }

        // 生成签名并验证
        $data   = $request->except('signature');
        $secret = config('app.api_secret'); // 应用密钥
        if ($signature !== $this->generateSignature($data, $secret)) {
            return false;
        }

        Cache::put($cacheKey, true, $ttl);

        return true;
    }

    /**
     * 生成签名
     */
    private function generateSignature(array $data, string $secret): string
    {
        ksort($data); // 参数排序
        $string = http_build_query($data) . $secret;
        return hash('sha256', $string);
    }

    /**
     * 设备指纹验证
     */
    private function validateDevice(Request $request): bool
    {
        $deviceId = $request->header('X-Device-Id');
        if (empty($deviceId)) {
            return false;
        }

        // 存储设备信息（实际应用中可以使用更复杂的设备指纹方案）
        $key    = 'device:' . $deviceId;
        $userId = auth()->id() ?? 'guest';

        return Cache::remember($key, 86400, fn() => $userId) === $userId;
    }

    /**
     * 判断是否需要验证码
     */
    private function needsCaptcha(Request $request): bool
    {
        // 敏感接口需要验证码
        return Str::contains($request->path(), ['login', 'register', 'payment']);
    }

    /**
     * 验证验证码
     */
    private function validateCaptcha(Request $request): bool
    {
        // 简化示例，实际应用中应集成验证码服务
        return $request->input('captcha') === session('captcha_code');
    }

    /**
     * 阻止请求并返回错误响应
     */
    private function blockRequest(string $message, int $status = 403): Response
    {
        abort($status, $message);
    }
}
