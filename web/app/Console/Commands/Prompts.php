<?php

namespace App\Console\Commands;

use App\Models\Article;
use GuzzleHttp\RequestOptions;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use function Laravel\Prompts\search;
use function Laravel\Prompts\spin;
use function Laravel\Prompts\text;

class Prompts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'prompts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        while (true) {
            $question = text('和AI对话：');
            $response = spin(
                function () use ($question) {
                    $client   = new \GuzzleHttp\Client();
                    $response = $client->post('https://api.moonshot.cn/v1/chat/completions', [
                        RequestOptions::HEADERS => [
                            'Authorization' => 'Bearer sk-tJ7mJv9POmflg9DlDPJyWKn3DULtvvwMcr1MbmufYPOBfGcm',
                            'Content-Type'  => 'application/json',
                        ],
                        RequestOptions::STREAM  => true,
                        RequestOptions::JSON    => [
                            'model'       => 'moonshot-v1-8k',
                            'messages'    => [
                                [
                                    'role'    => 'user',
                                    'content' => $question,
                                ]
                            ],
                            'temperature' => 0.3,
                            'stream'      => true,
                        ],
                    ]);
                    $stream   = $response->getBody();
                    $buffer   = '';
                    $answer   = '';
                    while (!$stream->eof()) {
                        $buffer   .= $stream->read(24);
                        $position = strpos($buffer, "\n\n");
                        if ($position === false) {
                            continue;
                        }
                        $firstPart = substr($buffer, 0, $position + 2);
                        $json      = trim(substr($firstPart, 6));
                        $data      = json_decode($json, true);
                        $buffer    = substr($buffer, $position + 2);
                        $answer    .= Arr::get($data, 'choices.0.delta.content', '');
                    }

                    return $answer;
                },
                'AI正在努力思考中...' . PHP_EOL,
            );
            $arr      = mb_str_split($response);
            echo '    ';
            foreach ($arr as $value) {
                fwrite(STDOUT, $value);
                usleep(50000);
            }
            fwrite(STDOUT, PHP_EOL . PHP_EOL);
        }

        while (true) {
            search(
                '请输入搜索词',
                fn(string $value) => strlen($value) > 0
                    ? Article::where('title', 'like', "%{$value}%")
                             ->get()
                             ->map(function (Article $article) {
                                 $article->title = $article->title . ' ' . route('articles.show', $article->uuid);
                                 return $article;
                             })
                             ->pluck('title', 'uuid')
                             ->all()
                    : []
            );
        }
    }
}
