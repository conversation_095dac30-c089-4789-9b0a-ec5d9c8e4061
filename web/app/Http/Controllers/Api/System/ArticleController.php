<?php

namespace App\Http\Controllers\Api\System;

use App\Enum\ArticleFlag;
use App\Http\Controllers\Controller;
use App\Http\Requests\ArticleStoreRequest;
use App\Http\Requests\ArticleUpdateRequest;
use App\Http\Resources\ArticleCollection;
use App\Http\Resources\ArticleResource;
use App\Models\Article;
use App\Models\User;
use App\Utils\Http\Api;
use Fig\Http\Message\StatusCodeInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;

class ArticleController extends Controller
{
    public function index(Request $request)
    {
        $articles = $request->user()
            ->articles()
            ->when($request->query('keyword'), function (Builder $query, $keyword) {
                $query->where('title', 'like', "%$keyword%");
            })
            ->when((int)$request->query('featured'), function (Builder $query, $featured) {
                $query->where('featured', $featured);
            })
            ->latest()
            ->paginate()
            ->appends($request->query->all());
        return Api::okay(new ArticleCollection($articles));
    }

    public function show($id): JsonResponse
    {
        $article = Article::with(['tags', 'files', 'user' => fn($query) => $query->withCount(['articles'])])
            ->where('uuid', $id)
            ->firstOrFail();

        //        Queue::push(new LogViews($article, request(), auth()->user()));
        if (!Auth::guard('api')->user() && ($article->flag & ArticleFlag::needLoginWhenRead->value) !== 0) {
            $article->makeHidden(['content', 'files']);
        }

        $article->timestamps = false;
        $article->increment('views');

        return Api::okay(new ArticleResource($article));
    }

    /**
     * @throws Throwable
     */
    public function store(ArticleStoreRequest $request): JsonResponse
    {
        $data = $request->validated();
        $article = DB::transaction(function () use ($data, $request) {
            /** @var Article $article */
            $article = $request->user()->articles()->create($data);
            if (isset($data['tags'])) {
                $article->tags()->attach($data['tags']);
            }
            return $article;
        });

        return Api::okay($article, StatusCodeInterface::STATUS_CREATED);
    }

    /**
     * @throws Throwable
     */
    public function update($id, ArticleUpdateRequest $request): JsonResponse
    {
        /** @var Article $article */
        $article = $request->user()->articles()->where('uuid', $id)->firstOrFail();
        $data = $request->validated();
        DB::transaction(function () use ($article, $data) {
            $article->update($data);
            if (isset($data['tag_id'])) {
                $article->tags()->sync($data['tag_id']);
            }
        });

        return Api::okay();
    }

    /**
     * 删除文章
     *
     * @throws Throwable
     */
    public function destroy($id, Request $request): JsonResponse
    {
        /**
         * @var User $user
         * @var Article $article
         */
        $user = $request->user();
        $article = $user->articles()->where('uuid', $id)->firstOrFail();
        DB::transaction(function () use ($article) {
            $article->delete();
            $article->tags()->detach();
            $article->comments()->delete();
        });

        return Api::okay();
    }
}
