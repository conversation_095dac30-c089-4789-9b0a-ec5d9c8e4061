<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;

class ConvertPDF2Images extends Command
{
    protected $name = 'convert:pdf';

    protected function configure()
    {
        $this->addArgument('file', InputArgument::REQUIRED);
    }

    public function handle()
    {
        $file     = $this->input->getArgument('file');
        $pathInfo = pathinfo($file);
        $path     = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        if (!is_dir($imagesPath = $path . '/' . $filename)) {
            mkdir($imagesPath, 0755, true);
        }
        $imagick = new \Imagick();
        $imagick->setResolution(120, 120);
        $imagick->setCompressionQuality(100);
        $imagick->readImage($file);
        foreach ($imagick as $key => $image) {
            $image->setImageFormat('webp');
            $image->setImageCompressionQuality(100);
            $width     = $image->getImageWidth();
            $height    = $image->getImageHeight();
            $newWidth  = 600;
            $radio     = $newWidth / $width;
            $newHeight = $radio * $height;
            $image->resizeImage($newWidth, $newHeight, \Imagick::FILTER_LANCZOS, 1);
            $image->writeImage($imagesPath . '/' . $key + 1 . '.webp');
        }
        $image->clear();
        $image->destroy();
    }
}
