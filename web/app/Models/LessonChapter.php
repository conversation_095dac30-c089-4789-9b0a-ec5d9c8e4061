<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property \Illuminate\Support\Collection $sections
 */
class LessonChapter extends Model
{
    use SoftDeletes;

    protected $table    = 'lesson_chapters';
    protected $fillable = ['title', 'cover', 'sort'];

    public function sections(): HasMany
    {
        return $this->hasMany(LessonSection::class, 'chapter_id', 'id')->oldest('sort');
    }

    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class, 'lesson_id', 'id');
    }
}
