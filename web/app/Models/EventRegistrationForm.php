<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property bool   $enable
 * @property Carbon $start_time
 * @property Carbon $end_time
 */
class EventRegistrationForm extends Model
{
    use HasFactory, SoftDeletes;

    protected $table      = 'event_registration_forms';
    protected $fillable   = ['name', 'intro', 'banner', 'enable', 'start_time', 'end_time'];
    protected $attributes = ['intro' => '', 'banner' => ''];
    protected $appends    = ['state'];
    protected $casts      = [
        'enable'     => 'boolean',
        'start_time' => 'datetime',
        'end_time'   => 'datetime',
    ];

    const STATE = [
        'notStarted' => 1,
        'started'    => 2,
        'ended'      => 3,
    ];

    public function getStateAttribute()
    {
        $now = Carbon::now();
        if ($now->lt($this->start_time)) {
            return self::STATE['notStarted'];
        }

        if ($now->gt($this->end_time)) {
            return self::STATE['ended'];
        }

        return self::STATE['started'];
    }

    public function fields(): HasMany
    {
        return $this->hasMany(EventRegistrationField::class, 'form_id')->orderBy('sort');
    }

    public function registrations(): HasMany
    {
        return $this->hasMany(EventRegistration::class, 'form_id');
    }
}
