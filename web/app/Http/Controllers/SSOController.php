<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Utils\DeviceDetector;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Laravel\Socialite\AbstractUser;
use Laravel\Socialite\Contracts\Factory;
use Laravel\Socialite\Facades\Socialite;
use Next\Socialite\PassportProvider;

class SSOController extends Controller
{
    public function __construct()
    {
        app(Factory::class)->extend('sso', function ($app) {
            $config   = config('socialite.passport');
            $provider = $config['provider'];
            $config   = $config['config'];
            return new $provider(
                $app['request'], $config['client_id'],
                $config['client_secret'], $config['redirect']
            );
        });
    }

    const INTENDED_URL_KEY = 'login.redirect_uri';

    public function login(Request $request): HttpResponse|RedirectResponse
    {
        $request->session()->put(self::INTENDED_URL_KEY, $request->query('redirect_uri'));

        return Socialite::driver('sso')->redirect();
    }

    public function loginCallback(Request $request)
    {
        /**
         * @var User         $user
         * @var AbstractUser $socialiteUser
         */
        $socialiteUser = Socialite::driver('sso')->stateless()->user();
        $user          = User::query()->updateOrCreate(['id' => $socialiteUser->getId()], [
            'email'      => $socialiteUser->getEmail(),
            'nickname'   => $socialiteUser->getNickname(),
            'name'       => $socialiteUser->getName(),
            'avatar'     => $socialiteUser->getAvatar(),
            'attributes' => $socialiteUser,
        ]);

        Auth::login($user);

        if (app(DeviceDetector::class)->isWechatMiniProgram()) {
            return view('wechat.mini-program.launch', [
                'accessToken' => $user->createToken('api')->accessToken,
            ]);
        }

        return Response::redirectTo($request->session()->pull(self::INTENDED_URL_KEY, '/'));
    }

    public function logout(Request $request): RedirectResponse
    {
        $guard = Auth::guard('web');
        if (!$guard->guest()) {
            $guard->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        }

        $redirectURI = $request->query('redirect_uri', url()->previous());

        return Response::redirectTo(PassportProvider::getLogoutUrl($redirectURI));
    }
}
