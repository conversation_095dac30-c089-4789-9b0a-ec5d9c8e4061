<?php

namespace App\Console\Commands;

use App\Utils\ViewRecorder\Recorder;
use Illuminate\Console\Command;

class UpdateViews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-views';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将redis中的浏览量统计数据更新到数据库';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        app(Recorder::class)->sync(function ($class, $id, $views) {
            $this->components->info(sprintf('更新成功 type[%s] id[%s], views[%d]', $class, $id, $views));
        });
    }
}
