<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $structure
 */
class Lesson extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['user_id', 'name', 'description', 'cover'];

    const STRUCTURE_CHAPTER = 1;
    const STRUCTURE_SECTION = 2;

    public function chapters(): HasMany
    {
        return $this->hasMany(LessonChapter::class, 'lesson_id', 'id')->oldest('sort');
    }

    public function sections(): HasMany
    {
        return $this->hasMany(LessonSection::class, 'lesson_id', 'id');
    }
}
