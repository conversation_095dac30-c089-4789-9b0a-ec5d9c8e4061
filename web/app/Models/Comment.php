<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $user_id
 * @property string $content
 */
class Comment extends Model
{
    use SoftDeletes;
    use HasUuids;

    protected $table    = 'comments';
    protected $fillable = ['user_id', 'article_id', 'content', 'parent_id'];

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id', 'uuid');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function uniqueIds()
    {
        return ['uuid'];
    }
}
