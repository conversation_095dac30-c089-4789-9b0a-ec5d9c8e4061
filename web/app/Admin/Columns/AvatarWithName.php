<?php

namespace App\Admin\Columns;

use Dcat\Admin\Grid\Displayers\AbstractDisplayer;

class AvatarWithName extends AbstractDisplayer
{
    /**
     * 渲染头像和昵称
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    public function display($avatarField = 'avatar', $nameField = 'nickname')
    {
        if (empty($this->value)) {
            return '';
        }
        // 获取头像和昵称数据
        $avatar = $this->value[$avatarField] ?? '';
        $name   = $this->value[$nameField] ?? '';

        // 如果没有头像，使用默认头像
        if (empty($avatar)) {
            $avatar = 'https://picsum.photos/200/200?random=' . $this->value['id'] ?? $name;
        }

        $userUrl = route('dcat.admin.user.show', ['user' => $this->value['id']]);
        // 渲染 HTML
        return <<<HTML
    <a href="$userUrl"><img src="{$avatar}" alt="{$name}" title="{$name}" style="width: 40px; height: 40px; border-radius: 50%; margin-right: 10px;"></a>
HTML;
    }
}
