<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\User;
use App\Models\UserCheckIn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use OpenApi\Generator;

class IndexController extends Controller
{
    public function openapi(): HttpResponse
    {
        $openapi = Generator::scan([
            base_path('app/Http/Controllers'),
            base_path('app/Models')
        ]);

        return Response::make($openapi->toJson(), headers: [
            'Content-Type'                => 'application/json',
            'Access-Control-Max-Age'      => 3600,
            'Access-Control-Allow-Origin' => '*',
        ]);
    }

    public function index(Request $request)
    {
        $checkIn = null;
        if ($user = $request->user()) {
            $checkIn = UserCheckIn::query()
                ->where('user_id', $user->getKey())
                ->whereDate('checkin_at', now()->toDateString())
                ->first();
        }

        $checkInsCount = UserCheckIn::query()
            ->whereDate('checkin_at', now()->toDateString())
            ->count();
        $articles      = Article::featured()
            ->with(['user', 'tags'])
            ->withCount(['comments'])
            ->when($request->query('query'), fn(Builder $query, $keyword) => $query->where('title->' . app()->getLocale(), 'like', "%$keyword%"))
            ->simplePaginate()
            ->appends($request->only('query'));

        return view('index', compact('checkIn', 'checkInsCount', 'articles'));
    }

    public function findFriends()
    {
        return view('find-friend');
    }

    public function notifications(Request $request)
    {
        /** @var User $user */
        $user          = $request->user();
        $notifications = $user->notifications()->whereNull('read_at')->simplePaginate(10);
        $notifications->getCollection()->transform(function (DatabaseNotification $notification) {
            $user = User::query()->find($notification['data']['from_user']);

            $notification['user'] = $user;
            return $notification;
        });

        return view('notification', compact('notifications'));
    }

    public function checkins()
    {
        $checkins = UserCheckIn::with(['user'])
            ->whereHas('user')
            ->whereDate('checkin_at', now()->toDateString())
            ->oldest('ranking')
            ->get();

        return view('checkins', compact('checkins'));
    }
}
