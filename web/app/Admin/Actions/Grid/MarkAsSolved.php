<?php

namespace App\Admin\Actions\Grid;

use App\Jobs\Wechat\SendSubScribeMessage;
use App\Models\Feedback;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class MarkAsSolved extends RowAction
{
    /**
     * @return string
     */
    protected $title = '标记为已解决';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $feedback = Feedback::query()->findOrFail($this->getKey());
        $feedback->markedAsSolved();
        SendSubScribeMessage::dispatch($feedback);

        return $this->response()
            ->success('Processed successfully.')
            ->refresh();
    }

    /**
     * @return string|void
     */
    protected function href()
    {
        // return admin_url('auth/users');
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['Confirm?', 'contents'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
