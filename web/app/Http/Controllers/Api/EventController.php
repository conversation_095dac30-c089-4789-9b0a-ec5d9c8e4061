<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\RegistrationFieldStoreRequest;
use App\Http\Requests\RegistrationFormStoreRequest;
use App\Http\Requests\RegistrationFormUpdateRequest;
use App\Http\Requests\ActivityStoreRequest;
use App\Http\Requests\ActivityUpdateRequest;
use App\Http\Resources\EventCollection;
use App\Models\Activity;
use App\Models\EventRegistration;
use App\Models\EventRegistrationDefaultField;
use App\Models\EventRegistrationValue;
use App\Utils\Http\Api;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;

class EventController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api')->except(['index', 'show', 'messages']);
        parent::__construct();
    }

    public function index(Request $request)
    {
        $rooms = Activity::query()->when($request->query('state'), function ($query, $state) {
            $query->where('state', $state);
        })->paginate();
        return Api::okay(new EventCollection($rooms));
    }

    public function store(ActivityStoreRequest $request)
    {
        /** @var Activity $room */
        $room = $request->user()->activities()->create($request->validated());
        $room->registrationForms()->create(['name' => '活动报名']);
        return Api::okay($room);
    }

    public function show($id)
    {
        return Api::okay(Activity::with(['user'])->findOrFail($id));
    }

    public function registrationForms(Activity $room)
    {
        return Api::okay($room->registrationForms()->withCount(['registrations'])->get());
    }

    public function update($id, ActivityUpdateRequest $request)
    {
        $input = $request->validated();
        $room  = $request->user()->activities()->findOrFail($id);
        $room->update($input);

        return Api::okay();
    }

    /**
     * @throws Exception
     */
    public function destroy($id, Request $request)
    {
        $room = $request->user()->activities()->findOrFail($id);
        if ($room->isState(Activity::STATE['living'])) {
            throw new Exception('正在直播中，请先结束直播');
        }

        $room->delete();

        return Api::okay();
    }

    public function start($roomId, Request $request)
    {
        $room = $request->user()->activities()->findOrFail($roomId);
        if ($room->isState(Activity::STATE['living'])) {
            throw new Exception('房间已经开始');
        }
        $updates = [
            'state'           => Activity::STATE['living'],
            'real_start_time' => Carbon::now(),
            'end_time'        => null,
        ];
        if (empty($room->stream_push_token)) {
            $updates['stream_push_token'] = Str::random(40);
        }
        $room->update($updates);

        return Api::okay();
    }

    public function end($roomId, Request $request)
    {
        $room = $request->user()->activities()->findOrFail($roomId);
        if (!$room->isState(Activity::STATE['living'])) {
            throw new Exception('房间未开始直播，不能结束');
        }
        $room->update([
            'state'    => Activity::STATE['closed'],
            'end_time' => Carbon::now(),
        ]);

        return Api::okay();
    }

    /**
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function chat(Activity $room, Request $request)
    {
        if (!$room->isState(Activity::STATE['living'])) {
            throw new Exception('房间未开始');
        }
        $input = $this->validate($request, [
            'type'    => 'required|in:text,image',
            'content' => 'required|max:255',
        ]);

        if (!$content = $request->input('content')) {
            throw new InvalidArgumentException('内容不能为空');
        }

        $sanitize = text_sanitize($content);
        if ($sanitize->isIllegal()) {
            throw new InvalidArgumentException('发现违禁词，已被过滤');
        }

        $user = $request->user();

        return Api::okay();
    }

    public function registrations(Activity $room, $formId)
    {
        $form          = $room->registrationForms()->findOrFail($formId);
        $registrations = $form->registrations()->with(['values', 'user'])->paginate();
        $registrations->getCollection()->transform(function (EventRegistration $registration) {
            $values = [];
            $registration->values->each(function (EventRegistrationValue $value) use (&$values) {
                $values[$value->name] = $value->value;
            });
            $registration           = $registration->only(['id', 'user', 'created_at']);
            $registration['values'] = $values;

            return $registration;
        });

        return Api::okay($registrations);
    }

    public function registrationFields(): JsonResponse
    {
        return Api::okay(EventRegistrationDefaultField::with(['children'])->where('parent_id', 0)->get());
    }

    public function getRegistrationForm(Activity $room)
    {
        return Api::okay($room->registrationForms()->first());
    }

    public function showRegistrationForm(Activity $room, $formId)
    {
        return Api::okay($room->registrationForms()->with(['fields'])->findOrFail($formId));
    }

    public function createRegistrationForm(Activity $room, RegistrationFormStoreRequest $request)
    {
        $form = $room->registrationForms()->create($request->validated());

        return Api::okay($form->load(['fields']));
    }

    public function deleteRegistrationForm(Activity $room, $formId)
    {
        $room->registrationForms()->delete($formId);

        return Api::okay();
    }

    public function updateRegistrationForm(Activity $room, $formId, RegistrationFormUpdateRequest $request)
    {
        $form = $room->registrationForms()->findOrFail($formId);
        $form->update($request->validated());

        return Api::okay($form->load(['fields']));
    }

    public function fields(Activity $room)
    {
        return Api::okay($room->registrationForm->fields);
    }

    /**
     * @throws Exception
     */
    public function addFields(Activity $room, $formId, RegistrationFieldStoreRequest $request)
    {
        $form   = $room->registrationForms()->findOrFail($formId);
        $inputs = $request->safe(['name']);
        if ($form->fields()->where($inputs)->exists()) {
            throw new Exception('字段已存在');
        }

        $field = $form->fields()->create($request->safe()->all());

        return Api::okay($field);
    }

    public function deleteField(Activity $room, $formId, $fieldId)
    {
        $room->registrationForms()->findOrFail($formId)->fields()->where('id', $fieldId)->delete();

        return Api::okay();
    }

    public function updateField(Activity $room, $formId, $fieldId, Request $request)
    {
        $field = $room->registrationForms()->findOrFail($formId)->fields()->findOrFail($fieldId);
        $field->update($request->all());

        return Api::okay();
    }
}
