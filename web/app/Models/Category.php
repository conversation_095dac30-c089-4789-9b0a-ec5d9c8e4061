<?php

namespace App\Models;

use Dcat\Admin\Traits\ModelTree;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Spatie\EloquentSortable\Sortable;

/**
 * @property int        $id
 * @property string     $uuid
 * @property int        $parent_id
 * @property string     $slug
 * @property string     $name
 * @property string     $description
 * @property string     $icon
 * @property boolean    $enabled
 * @property static     $parent
 * @property Collection $children
 * @method static Builder enabled(bool $enabled = true)
 */
class Category extends Model implements Sortable
{
    use SoftDeletes, HasUuids;
    use ModelTree;

    protected $fillable     = ['name', 'parent_id', 'icon', 'enabled'];
    protected $hidden       = ['pivot', self::DELETED_AT];
    protected $casts        = ['enabled' => 'boolean'];
    protected $titleColumn  = 'name';
    protected $orderColumn  = 'order';
    protected $parentColumn = 'parent_id';

    public function uniqueIds()
    {
        return ['uuid'];
    }

    public function articles(): MorphToMany
    {
        return $this->morphedByMany(Article::class, 'model', 'model_has_categories', 'category_id', null, 'id', 'id');
    }

    public function stickers(): MorphToMany
    {
        return $this->morphedByMany(Sticker::class, 'model', 'model_has_categories', 'category_id', null, 'id', 'id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * 启用的分类
     */
    protected function scopeEnabled(Builder $query, bool $enabled = true): void
    {
        $query->where('enabled', $enabled);
    }
}
