<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\CategoryStoreRequest;
use App\Http\Requests\CategoryUpdateRequest;
use App\Models\Category;
use App\Utils\Http\Api;

class CategoryController extends Controller
{
    public function index()
    {
        return Api::okay(Category::query()->paginate());
    }

    public function store(CategoryStoreRequest $request)
    {
        return Api::okay(Category::query()->create($request->validated()));
    }

    public function update(Category $category, CategoryUpdateRequest $request)
    {
        $category->update($request->validated());

        return Api::okay();
    }
}
