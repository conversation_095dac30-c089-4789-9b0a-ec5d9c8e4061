<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int   $flag
 * @property int   $points
 * @property array $points_rules
 */
class Event extends Model
{
    use SoftDeletes, HasUuids;

    protected $table    = 'events';
    protected $fillable = [
        'name',
        'slug',
        'points',
        'points_rules',
        'flag',
    ];
    protected $casts    = [
        'points'       => 'decimal:2',
        'points_rules' => 'array',
        'flag'         => 'integer',
    ];

    /**
     * 一些标识
     */
    const FLAG = [
        'disabled'              => 0x01, // 是否禁用
        'customizable'          => 0x02, // 积分数量可自定义的
        'recordZeroCreditEvent' => 0x04, // 是否记录零积分的事件
    ];

    const POINTS_RULES = [
        'countPerScene' => 'count_per_scene', // 同一场景的积分获取次数限制
        'countPerDay'   => 'count_per_day',   // 同一天相同的resource可以获取几次积分
        'countPerWeek'  => 'count_per_week',  // 同一周相同的resource可以获取几次积分
    ];

    const SLAG_STICKER_VIEW     = 'StickerView';
    const SLUG_STICKER_FAVORITE = 'StickerFavorite';

    public function uniqueIds()
    {
        return ['uuid'];
    }
}
