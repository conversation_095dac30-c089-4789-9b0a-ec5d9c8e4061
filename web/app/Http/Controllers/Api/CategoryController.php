<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CategoryCollection;
use App\Models\Category;
use App\Utils\Http\Api;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('api.protection')->only(['index', 'show']);
        parent::__construct();
    }

    public function index(Request $request)
    {
        $this->validate($request, [
            'parent_id' => 'uuid',
        ]);
        $parentCategory = null;
        if ($parentId = $request->query('parent_id')) {
            $parentCategory = Category::query()
                ->where('uuid', $parentId)
                ->first();
        }

        if ($parentCategory && !$parentCategory->enabled) {
            return Api::okay();
        }

        $categories = Category::enabled()
            ->orderBy('order')
            ->where('parent_id', (int)$parentCategory?->getKey())->get();

        return Api::okay(new CategoryCollection($categories));
    }

    public function show($id)
    {
        return Api::okay(
            Category::enabled()
                ->with([
                    'children' => fn($query) => $query->where('enabled', true)
                        ->orderBy('order'),
                ])
                ->where('uuid', $id)
                ->firstOrFail()
        );
    }

    public function namedCategory(Request $request)
    {
        $this->validate($request, ['name' => 'required', 'main' => 'boolean']);

        $category = Category::enabled()
            ->with(['children' => fn($query) => $query->orderBy('order')])
            ->where('name', $request->input('name'))
            ->when($request->input('main', true), fn($query) => $query->where('parent_id', 0))
            ->first();

        return Api::okay($category);
    }
}
