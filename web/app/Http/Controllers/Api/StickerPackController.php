<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\StickerPackCollection;
use App\Models\StickerPack;
use App\Utils\Http\Api;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class StickerPackController extends Controller
{
    public function __construct()
    {
        $this->middleware('api.protection')->only('index', 'show');
        $this->middleware('auth:api')->except(['index', 'show']);
        parent::__construct();
    }

    public function index(Request $request)
    {
        $stickers = StickerPack::query()
            ->when(
                $request->query('keyword'),
                fn(Builder $query, $keyword) => $query->where('name', 'like', sprintf('%%%s%%', $keyword))
            )
            ->paginate(page_size_resolver(18, 18));

        return Api::okay(new StickerPackCollection($stickers));
    }

    public function show($id)
    {
        $pack = StickerPack::with(['stickers.pack:id,uuid,name,image'])
            ->where('uuid', $id)
            ->firstOrFail();

        return Api::okay($pack);
    }
}
