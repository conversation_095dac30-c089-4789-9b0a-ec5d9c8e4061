<?php

namespace App\Http\Middleware;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Guard;

class CheckPermissions
{
    /**
     * @param Request $request
     * @param \Closure(Request): Response $next
     * @return void
     */
    public function handle(Request $request, \Closure $next)
    {
        /** @var User $user */
        $user = $request->user();
        if ($user->isRoot()) {
            return $next($request);
        }

        $routeName = Route::currentRouteName();
        if (!$user->hasPermissionTo($routeName, Guard::getDefaultName($user))) {
            abort(403, 'Permission denied');
        }

        return $next($request);
    }
}
