<?php

namespace App\Rules;

use Closure;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class TCaptcha implements ValidationRule, DataAwareRule
{
    protected array $data;

    public function setData(array $data)
    {
        $this->data = $data;

        return $this;
    }

    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $ticket  = Arr::get($this->data, 'ticket');
        $randStr = Arr::get($this->data, 'randstr');
        if (!$ticket || !$randStr) {
            $fail('验证失败');
        }
        $client = new Client([
            'base_uri'              => 'https://cgi.urlsec.qq.com',
            RequestOptions::TIMEOUT => 5,
            RequestOptions::VERIFY  => false,
        ]);
        try {
            $response = $client->get('/index.php', [
                'query'   => [
                    'm'        => 'check',
                    'a'        => 'gw_check',
                    'callback' => 'url_query',
                    'url'      => 'https://www.qq.com/' . rand(111111, 999999),
                    'ticket'   => $ticket,
                    'randstr'  => $randStr,
                ],
                'headers' => [
                    'Accept'          => 'application/json',
                    'Accept-Language' => 'zh-CN,zh;q=0.8',
                    'Connection'      => 'close',
                    'Referer'         => 'https://urlsec.qq.com/check.html',
                    'User-Agent'      => $this->randomUserAgent(),
                ],
            ]);
            $contents = $response->getBody()->getContents();
            $contents = substr($contents, 10, -1);
            $data     = json_decode($contents, true);
            Log::debug('[tCaptcha] 响应', ['data' => $data]);
            if (Arr::get($data, 'data.retcode') !== 0) {
                $fail('验证失败');
                Log::debug('[tCaptcha] 验证失败', [
                    'request'  => request()->all(),
                    'response' => $data,
                ]);
            }
        } catch (GuzzleException $e) {
            Log::error('[tCaptcha] ' . $e->getMessage(), request()->all());
            $fail('验证失败');
        }
    }

    protected function randomUserAgent()
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/118.0.2088.61',
            'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
            'Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8J2 Safari/6533.18.5',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.101 Safari/537.36',
        ];

        return array_rand($userAgents);
    }
}
