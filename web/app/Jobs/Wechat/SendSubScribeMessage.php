<?php

namespace App\Jobs\Wechat;

use App\Models\Feedback;
use App\Models\SocialiteUser;
use App\Models\WechatApp;
use GuzzleHttp\RequestOptions;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendSubScribeMessage implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected Feedback $feedback,
    )
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /** @var WechatApp $wechatApp */
        $wechatApp     = WechatApp::miniProgram()->first();
        $socialiteUser = SocialiteUser::query()->where([
            'user_id'  => $this->feedback->user->getKey(),
            'provider' => SocialiteUser::PROVIDER['wechat_miniprogram'],
            'app_id'   => $wechatApp->app_id,
        ])->first();
        $service       = $wechatApp->getService();
        $service->getClient()->request('POST', 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send', [
            RequestOptions::JSON => [
                'template_id'       => 'yveoI2WoAVBxAgveEMbMFaO64NPls9Vi8le1edwRXiY',
                'page'              => 'pages/index/index',
                'touser'            => $socialiteUser->identity,
                'data'              => [
                    'date3'   => [
                        'value' => $this->feedback->created_at->toDateString(),
                    ],
                    'date4'   => [
                        'value' => $this->feedback->solved_at->toDateString(),
                    ],
                    'thing34' => [
                        'value' => '您反馈的问题已处理',
                    ],
                    'thing8'  => [
                        'value' => '点击查看详情',
                    ],
                ],
                'miniprogram_state' => app()->isProduction() ? 'formal' : 'trial',
                'lang'              => 'zh_CN',
            ]
        ]);
    }
}
