<?php

namespace App\Http\Controllers\WebAuthn;

use App\Models\WebAuthnCredential;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laragear\WebAuthn\Http\Requests\AttestationRequest;
use Laragear\WebAuthn\Http\Requests\AttestedRequest;

use function response;

class WebAuthnRegisterController
{
    /**
     * Returns a challenge to be verified by the user device.
     */
    public function options(AttestationRequest $request): Responsable
    {
        return $request
            ->fastRegistration()
//            ->userless()
//            ->allowDuplicates()
            ->toCreate();
    }

    public function createChallenge(AttestationRequest $request)
    {
        return $request->toCreate();
    }

    /**
     * Registers a device for further WebAuthn authentication.
     */
    public function register(AttestedRequest $request): Response
    {
        $request->save();

        return response()->noContent();
    }

    /**
     * Remove a WebAuthn credential.
     */
    public function destroy(Request $request, WebAuthnCredential $credential)
    {
        // Ensure the credential belongs to the authenticated user
        if ($credential->authenticatable_id !== $request->user()->id) {
            abort(403);
        }

        $credential->delete();

        return response()->noContent();
    }
}
