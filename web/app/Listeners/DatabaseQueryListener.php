<?php

namespace App\Listeners;

use Carbon\CarbonInterface;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\Log;

class DatabaseQueryListener
{
    /**
     * @param object $event
     */
    public function handle(object $event): void
    {
        if ($event instanceof QueryExecuted) {
            try {
                $bindings = array_map(function ($binding) {
                    return match (true) {
                        is_string($binding) => "'$binding'",
                        $binding instanceof \DateTimeInterface => $binding->format("'Y-m-d H:i:s'"),
                        default => $binding,
                    };
                }, $event->bindings);
                $sql      = sprintf(str_replace('?', '%s', $event->sql), ...$bindings);
                Log::info('sql', [
                    'query'      => $sql,
                    'bindings'   => $event->bindings,
                    'connection' => $event->connectionName,
                    'database'   => $event->connection->getDatabaseName(),
                    'time'       => $event->time,
                ]);
            } catch (\Throwable) {
            }
        }
    }
}
