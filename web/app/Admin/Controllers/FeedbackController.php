<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\MarkAsSolved;
use App\Admin\Repositories\Feedback;
use App\Models\Model;
use App\Models\Sticker;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class FeedbackController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Feedback(['model']), function (Grid $grid) {
            $grid->model()->orderByDesc('created_at');
            $grid->column('model.image')
                ->when(true, function (Grid\Column $column) {
                    return $column->image('', 100, 100);
                });
            $grid->column('feedback');
            $grid->column('solved_at')->sortable();
            $grid->column('created_at')->sortable();
//            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append(new MarkAsSolved);
                // $actions->disableDelete(); //  禁用删除
                $actions->disableEdit();   //  禁用修改
                $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                $actions->disableView(); //  禁用查看
            });
            $grid->disableCreateButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Feedback(), function (Show $show) {
            $show->field('id');
            $show->field('feedback');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Feedback(), function (Form $form) {
            $form->display('id');
            $form->text('feedback');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
