<?php

namespace App\Listeners;

use App\Models\UserClientGrant;
use Laravel\Passport\Events\AccessTokenCreated;

class LogUserClientGrant
{
    /**
     * Handle the event.
     */
    public function handle(AccessTokenCreated $event): void
    {
        $attributes = [
            'user_id' => $event->userId,
            'client_id' => $event->clientId,
        ];
        if ($userClientGrant = UserClientGrant::query()->where($attributes)->first()) {
            $userClientGrant->touch();
        } else {
            UserClientGrant::query()->create($attributes);
        }
    }
}
