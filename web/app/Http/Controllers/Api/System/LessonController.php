<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\LessonStoreRequest;
use App\Http\Requests\LessonUpdateRequest;
use App\Models\Lesson;
use App\Utils\Http\Api;

class LessonController extends Controller
{
    public function index()
    {
        return Api::okay($this->user->lessons()->latest('id')->paginate());
    }

    public function show(Lesson $lesson)
    {
        $lesson->load(['chapters.sections']);

        return Api::okay($lesson);
    }

    public function store(LessonStoreRequest $request)
    {
        $lesson = $this->user->lessons()->create($request->validated());

        return Api::okay($lesson);
    }

    public function update(Lesson $lesson, LessonUpdateRequest $request)
    {
        $lesson->update($request->validated());

        return Api::okay();
    }
}
