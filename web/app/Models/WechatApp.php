<?php

namespace App\Models;

use App\Enum\WechatAppType;
use EasyWeChat\Kernel\Exceptions\InvalidArgumentException;
use EasyWeChat\MiniApp\Application as MiniApp;
use EasyWeChat\OfficialAccount\Application as OfficialAccount;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string        $app_id
 * @property WechatAppType $type
 * @method static Builder miniProgram()       // 小程序
 * @method static Builder officialAccount()   // 公众号
 */
class WechatApp extends Model
{
    use SoftDeletes;

    protected $table    = 'wechat_apps';
    protected $fillable = ['name', 'type', 'app_id', 'secret', 'token', 'aes_key'];
    protected $casts    = ['type' => WechatAppType::class];

    const TYPE = [
        'officialAccount' => 1,
        'miniProgram'     => 2,
    ];

    /**
     * @throws InvalidArgumentException
     */
    public function getService(): OfficialAccount|MiniApp
    {
        $config = $this->only(['app_id', 'secret', 'token', 'aes_key']);
        return match ($this->type) {
            WechatAppType::OfficialAccount => new OfficialAccount($config),
            WechatAppType::MiniProgram => new MiniApp($config),
            default => throw new \InvalidArgumentException('不支持的微信App类型'),
        };
    }

    protected function scopeMiniProgram($query)
    {
        return $query->where('type', self::TYPE['miniProgram']);
    }

    protected function scopeOfficialAccount($query)
    {
        return $query->where('type', self::TYPE['officialAccount']);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
