<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $user_id
 * @property int $login_type
 */
class LoginLog extends Model
{
    use HasFactory;

    protected $table    = 'login_logs';
    protected $fillable = ['user_id', 'login_type', 'ip', 'location', 'ua', 'device'];
    protected $casts    = [
        'user_id'    => 'integer',
        'login_type' => 'integer',
    ];

    const LOGIN_TYPE = [
        'password' => 1,
        'github'   => 2,
        'wechat'   => 3,
        'qq'       => 4,
        'weibo'    => 5,
        'gitee'    => 6,
        'alipay'   => 7,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
