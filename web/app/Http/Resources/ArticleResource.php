<?php

namespace App\Http\Resources;

use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

/**
 * @mixin Article
 */
class ArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $article = parent::toArray($request);
        $article['cover'] = storage_url($this['cover']);
        if (isset($article['content']) && $request->query('format') === 'html') {
            $article['content'] = markdown2html((string)$article['content']);
        }
        if (isset($article['author'])) {
            $article['author'] = Arr::only($article['author'], ['nickname', 'avatar']);
        }
        $article['url'] = route('articles.show', ['article' => $article['uuid']]);
        if ($this->whenLoaded('tags')) {
            $article['tags'] = array_map(fn($tag) => Arr::only($tag, ['id', 'name']), $this->tags->toArray());
        }
        return $article;
    }
}
