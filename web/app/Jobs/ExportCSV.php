<?php

namespace App\Jobs;

class ExportCSV
{
    public function __construct(
        protected readonly string   $file,
        protected readonly iterable &$data,
    )
    {
    }

    public function handle()
    {
        $fp = fopen($this->file, 'w+');
        fputs($fp, chr(239) . chr(187) . chr(191));   // utf-8 BOM头
        foreach ($this->data as &$item) {
            fputcsv($fp, $item);
        }
        fclose($fp);
    }

    protected function download()
    {
        header("Content-Type: application/octet-stream"); // 声明文件类型 以二进制流文件(可以是任何格式的文件)
// 根据浏览器类型 声明作为附件处理和下载后文件的名称
// Content-Disposition : 以什么方式下载 ； Content-Disposition:attachment ：以附件的形式下载
// 例如如果下载的文件是txt 用户下载时保存时命名为 1.txt
        if (preg_match("/MSIE/", $_SERVER['HTTP_USER_AGENT'])) {
            header('Content-Disposition:attachment;filename="export.csv"');
        } elseif (preg_match("/Firefox/", $_SERVER['HTTP_USER_AGENT'])) {
            header('Content-Disposition:attachment;filename*="export.csv"');
        } else {
            header('Content-Disposition:attachment;filename="export.csv"');
        }

// 用while(true) 和 sleep模拟分批查询，组装数据的过程
        while (true) {
            sleep(1);
            echo 'hello<br>'; // 模拟数据输出
            ob_flush();
            flush();
            if (connection_aborted()) {
                break;
            }
        }

    }
}
