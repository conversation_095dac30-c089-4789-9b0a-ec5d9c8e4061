<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use MongoDB\Laravel\Eloquent\SoftDeletes;

/**
 * @property string $name
 */
class StickerPack extends Model
{
    use SoftDeletes;
    use HasUuids;

    protected $table  = 'sticker_packs';

    protected $fillable = [
        'name',
        'image',
    ];

    public function uniqueIds()
    {
        return ['uuid'];
    }

    public function feedbacks(): MorphMany
    {
        return $this->morphMany(Feedback::class, 'model');
    }

    public function stickers(): HasMany
    {
        return $this->hasMany(Sticker::class, 'pack_id', 'id');
    }
}
