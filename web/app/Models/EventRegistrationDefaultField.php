<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventRegistrationDefaultField extends Model
{
    use HasFactory, SoftDeletes;

    protected $table    = 'event_registration_default_fields';
    protected $fillable = ['name', 'type', 'label', 'attributes', 'validations', 'parent_id'];
    protected $casts    = [
        'label'       => 'object',
        'validations' => 'object',
        'attributes'  => 'object',
    ];

    public function children(): HasMany
    {
        return $this->hasMany(EventRegistrationDefaultField::class, 'parent_id', 'id');
    }
}
