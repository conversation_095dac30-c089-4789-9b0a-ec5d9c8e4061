<?php

namespace App\Observers;

use App\Models\User;

class UserObserver
{
    public bool $afterCommit = true;

    /**
     * 处理用户「创建」事件。
     */
    public function created(User $user): void
    {
    }

    /**
     * 处理用户「更新」事件。
     */
    public function updated(User $user): void
    {
        // ...
    }

    /**
     * 处理用户「删除」事件。
     */
    public function deleted(User $user): void
    {
        // ...
    }

    /**
     * 处理用户「还原」事件。
     */
    public function restored(User $user): void
    {
        // ...
    }

    /**
     * 处理用户「强制删除」事件。
     */
    public function forceDeleted(User $user): void
    {
        // ...
    }
}
