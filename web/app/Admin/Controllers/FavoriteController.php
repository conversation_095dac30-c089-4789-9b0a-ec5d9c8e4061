<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\Date;
use App\Admin\Repositories\Favorite;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class FavoriteController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Favorite(['model', 'user']), function (Grid $grid) {
            $grid->model()->orderByDesc('created_at');
            $grid->column('user.nickname');
            $grid->column('model.image')->display(function ($image, Grid\Column $column) {
                if (!$image) {
                    return '';
                }
                $stickerUrl = route('dcat.admin.stickers.edit', ['sticker' => $column->getOriginalModel()->model_id]);
                return <<<HTML
<img data-action="preview-img" src="{$image}" style="max-width:200px;max-height:200px" class="img img-thumbnail"> <a href="$stickerUrl">编辑</a>
HTML;
            });
            $grid->column('created_at')->displayUsing(Date::class)->sortable();
            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                $actions->disableEdit();   //  禁用修改
                $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                $actions->disableView(); //  禁用查看
            });
            $grid->disableCreateButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Favorite(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('model_type');
            $show->field('model_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Favorite(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('model_type');
            $form->text('model_id');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
