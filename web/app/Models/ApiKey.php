<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class ApiKey extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'api_keys';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
