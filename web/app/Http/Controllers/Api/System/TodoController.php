<?php

namespace App\Http\Controllers\Api\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\TodoStoreRequest;
use App\Http\Requests\TodoUpdateRequest;
use App\Utils\Http\Api;
use Illuminate\Http\Request;

class TodoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return Api::okay($this->user->todos()->paginate());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TodoStoreRequest $request)
    {
        return Api::okay($this->user->todos()->create($request->validated()));
    }

    /**
     * Display the specified resource.
     */
    public function show($todoId)
    {
        return Api::okay($this->user->todos()->where('uuid', $todoId)->firstOrFail());
    }

    /**
     * Update the specified resource in storage.
     */
    public function update($todoId, TodoUpdateRequest $request)
    {
        $this->user->todos()->findOrFail($todoId)->update($request->validated());

        return Api::okay();
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($todoId)
    {
        $this->user->todos()->where('uuid', $todoId)->delete();

        return Api::okay();
    }
}
