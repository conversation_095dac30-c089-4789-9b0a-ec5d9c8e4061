<?php

namespace App\Services;

use Wrench\Client;

class SparkDeskService
{
    protected $appId = '892f6a61';

    public function getAuthAddr()
    {
        $method    = 'GET';
        $addr      = 'wss://spark-api.xf-yun.com/v2.1/chat';
        $apiKey    = '80d10b7da200858d4023f4c25fe2bb34';
        $apiSecret = 'NmVhYzhhM2FmNDM4M2RlNTYyMzBhN2Y0';
        if ($apiKey == "" && $apiSecret == "") { // 不鉴权
            return $addr;
        }

        $ul = parse_url($addr); // 解析地址
        if ($ul === false) { // 地址不对，也不鉴权
            return $addr;
        }

        // // $date = date(DATE_RFC1123); // 获取当前时间并格式化为RFC1123格式的字符串
        $timestamp      = time();
        $rfc1123_format = gmdate("D, d M Y H:i:s \G\M\T", $timestamp);
        // $rfc1123_format = "Mon, 31 Jul 2023 08:24:03 GMT";


        // 参与签名的字段 host, date, request-line
        $signString = array("host: " . $ul["host"], "date: " . $rfc1123_format, $method . " " . $ul["path"] . " HTTP/1.1");

        // 对签名字符串进行排序，确保顺序一致
        // ksort($signString);

        // 将签名字符串拼接成一个字符串
        $sgin = implode("\n", $signString);
        // 对签名字符串进行HMAC-SHA256加密，得到签名结果
        $sha                  = hash_hmac('sha256', $sgin, $apiSecret, true);
        $signature_sha_base64 = base64_encode($sha);

        // 将API密钥、算法、头部信息和签名结果拼接成一个授权URL
        $authUrl = "api_key=\"$apiKey\", algorithm=\"hmac-sha256\", headers=\"host date request-line\", signature=\"$signature_sha_base64\"";

        // 对授权URL进行Base64编码，并添加到原始地址后面作为查询参数
        return $addr . '?' . http_build_query(array(
                'host'          => $ul['host'],
                'date'          => $rfc1123_format,
                'authorization' => base64_encode($authUrl),
            ));
    }

    public function answer($messages = [], $userId = null)
    {
        $authAddr = $this->getAuthAddr();
        $client   = new Client($authAddr, 'https://spark-api.xf-yun.com');
        $client->connect();
        $request = [
            'header'    => [
                'app_id' => $this->appId,
                'uid'    => $userId ?: '12345',
            ],
            'parameter' => [
                'chat' => [
                    'domain'      => 'generalv2',
                    'temperature' => 0.5,
                    'max_tokens'  => 1024,
                ],
            ],
            'payload'   => [
                'message' => [
                    'text' => $messages,
                ],
            ],
        ];
        $client->sendData(json_encode($request));
        $data = '';
        while ($client->isConnected()) {
            if (is_array($buffer = $client->receive())) {
                $response = json_decode(current($buffer)->getPayload(), true);
                $data     .= ($response['payload']['choices']['text'][0]['content'] ?? '');
            }
        }
        $client->disconnect();

        return $data;
    }
}
