<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int          $type
 * @property int          $chapter_id
 * @property int          $sort
 * @property array|string $content
 */
class LessonSection extends Model
{
    use HasFactory, SoftDeletes;

    protected $table    = 'lesson_sections';
    protected $fillable = ['title', 'type', 'cover', 'sort', 'content'];
    protected $casts    = ['type' => 'integer'];
    protected $hidden   = [self::CREATED_AT, self::UPDATED_AT, 'deleted_at'];

    const TYPE_RICH_TEXT = 1;
    const TYPE_QUESTION  = 2;

    public function chapter(): BelongsTo
    {
        return $this->belongsTo(LessonChapter::class, 'chapter_id', 'id');
    }

    public function lesson(): BelongsTo
    {
        return $this->belongsTo(LessonChapter::class, 'lesson_id', 'id');
    }

    public function getContentAttribute($content)
    {
        if ($this->type === self::TYPE_QUESTION) {
            return json_decode($content, true) ?: [];
        }

        return (string)$content;
    }
}
