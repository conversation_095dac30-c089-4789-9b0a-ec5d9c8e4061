<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = parent::toArray($request);

        if ($this->resource instanceof LengthAwarePaginator) {
            $data = [
                'data'         => $this->collection,
                'total'        => $this->total(),
                'current_page' => $this->currentPage(),
                'per_page'     => $this->perPage(),
            ];
        }

        return $data;
    }
}
