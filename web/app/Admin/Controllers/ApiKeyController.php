<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\AvatarWithName;
use App\Admin\Repositories\ApiKey;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;
use Illuminate\Support\Str;

class ApiKeyController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ApiKey(['user']), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('user')->displayUsing(AvatarWithName::class);
            $grid->column('name');
            $grid->column('key');
            $grid->column('quota');
            $grid->column('used_quota');
            $grid->column('expired_at')->dateTime();
            $grid->column('is_active')->switch();
            $grid->column('created_at')->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ApiKey(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('name');
            $show->field('key');
            $show->field('quota');
            $show->field('used_quota');
            $show->field('expired_at');
            $show->field('is_active');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ApiKey(), function (Form $form) {
            $authorSelector = $form->select('user_id', 'Author')
                ->options(User::class, 'id', 'nickname')
                ->ajax('api/users');
            if ($form->isEditing()) {
                $authorSelector->value($form->model()->user_id);
            }
            $form->text('name');
            $form->text('key')->required()->default(Str::random(64))->minLength(64)->maxLength(64);
            $form->number('quota')->default(1000);
            $form->number('used_quota');
            $form->datetime('expired_at');
            $form->switch('is_active')->default(true);

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
