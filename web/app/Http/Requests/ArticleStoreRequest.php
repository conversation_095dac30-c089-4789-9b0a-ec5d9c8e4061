<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ArticleStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return !Auth::guest();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title'    => 'required|max:60',
            'abstract' => 'max:300',
            'content'  => 'string|nullable',
            'cover'    => 'string|nullable',
            'tags'     => 'array',
            'featured' => 'boolean',
            'draft'    => 'boolean',
        ];
    }

    public function attributes()
    {
        return __('models/user.attributes');
    }
}
