<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SystemLog;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class SystemLogController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SystemLog(), function (Grid $grid) {
            $grid->model()->orderByDesc('created_at');
            $grid->column('app_id');
            $grid->column('log')->width(400)->jsonSerialize();
            $grid->column('created_at')->sortable();
            $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                $actions->disableEdit();   //  禁用修改
                $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                $actions->disableView(); //  禁用查看
            });

            $grid->disableCreateButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('log');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SystemLog(), function (Show $show) {
            $show->field('id');
            $show->field('app_id');
            $show->field('log');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SystemLog(), function (Form $form) {
            $form->display('id');
            $form->text('app_id');
            $form->text('log');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
