<?php

namespace App\Admin\Controllers;

use App\Admin\Columns\AvatarWithName;
use App\Admin\Columns\Date;
use App\Admin\Repositories\WechatApp;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;

class WechatAppController extends AdminController
{
    /**
     * page index
     */
    public function index(Content $content)
    {
        return $content
            ->header('列表')
            ->description('全部')
            ->breadcrumb(['text' => '列表', 'url' => ''])
            ->body($this->grid());
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new WechatApp(['user']), function (Grid $grid) {
            $grid->column('type')->display(fn($value) => trans('wechat-app.options.type.' . $value->value));
            $grid->column('name');
            $grid->column('app_id');
            $grid->column('created_at')->displayUsing(Date::class)->sortable();
            // $grid->setActionClass(Grid\Displayers\Actions::class); // 行操作按钮显示方式 图标方式
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // $actions->disableDelete(); //  禁用删除
                // $actions->disableEdit();   //  禁用修改
                // $actions->disableQuickEdit(); //禁用快速修改(弹窗形式)
                // $actions->disableView(); //  禁用查看
            });

            $grid->setActionClass(Grid\Displayers\Actions::class);
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();


            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new WechatApp(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('type');
            $show->field('name');
            $show->field('app_id');
            $show->field('secret');
            $show->field('token');
            $show->field('aes_key');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new WechatApp(), function (Form $form) {
            $form->text('name');
            $form->radio('type')
                ->options(trans('wechat-app.options.type'))
                ->required()
                ->when(\App\Models\WechatApp::TYPE['miniProgram'], function (Form $form) {
                    $form->text('app_id');
                    $form->text('secret');
                })
                ->when(\App\Models\WechatApp::TYPE['officialAccount'], function (Form $form) {
                    $form->text('app_id');
                    $form->text('secret');
                    $form->text('token');
                    $form->text('aes_key');
                });
        });
    }
}
