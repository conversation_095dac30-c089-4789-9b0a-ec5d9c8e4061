<?php

namespace App\Models;

use App\Utils\Eloquent\Concern\HasFavoritedBy;
use App\Utils\Eloquent\Concern\HasUuids;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int                                $id
 * @property string                             $uuid
 * @property int                                $pack_id
 * @property string                             $name
 * @property string                             $image
 * @property int                                $views
 * @property string                             $user_id
 * @property ?User                              $user
 * @property ?StickerPack                       $pack
 * @property Collection|iterable<int, Category> $categories
 * @property Carbon                             $created_at
 * @property Carbon                             $updated_at
 */
class Sticker extends Model
{
    use SoftDeletes, HasUuids;
    use HasFavoritedBy;

    protected $fillable = ['name', 'image', 'order', 'pack_id', 'user_id', 'views'];
    protected $hidden   = ['pivot'];
    protected $casts    = ['views' => 'integer', 'pack_id' => 'integer', 'user_id' => 'integer'];

    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'model', 'model_has_tags')
            ->withTimestamps();
    }

    public function categories(): MorphToMany
    {
        return $this->morphToMany(Category::class, 'model', 'model_has_categories')
            ->withTimestamps();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function pack(): BelongsTo
    {
        return $this->belongsTo(StickerPack::class, 'pack_id', 'id');
    }

    public function feedbacks(): MorphMany
    {
        return $this->morphMany(Feedback::class, 'model');
    }
}
