<?php

return [
    'select'      => '浏览',
    'check_image' => '选择图片',
    'upload'      => '上传',
    'change_sort' => '点击切换排序',
    'create'      => '新建',
    'close'       => '关闭',
    'save'        => '确定',
    'prev'        => '上一页',
    'next'        => '下一页',
    
    'get_success'           => '获取成功',
    'upload_file_ext_error' => '上传文件格式不被允许',
    'upload_success'        => '上传成功',
    'upload_error'          => '上传失败',
    'create_success'        => '创建成功',
    'create_error'          => '创建失败',
    'create_dirname_empty'  => '文件夹名称不能为空',
    
    // js 使用语言包部分
    'js_empty'            => '空',
    'js_system_tip'       => '系统提示',
    'js_remove_tip'       => '确认要移除当前数据 [:data] 吗？',
    'js_select_type'      => '请选择:title',
    'js_page_render'      => '第:page页 / 共:total页，每页:perpage条',
    'js_dir_not_empty'    => '文件夹名称不能为空',
    'js_create_dir_error' => '创建文件夹失败',
    'js_upload_error'     => '上传失败',
    'js_selected_error'   => '选择数量不能超过 :num 条',
    'js_getdata_error'    => '数据获取失败',
    'js_preview_title'    => '预览',
    'js_preview'          => '预览',
    'js_remove'           => '移除',
    'js_dragsort'         => '拖动',
    'js_copy_success'     => '复制成功',
    'js_copy_error'       => '复制失败',
];
