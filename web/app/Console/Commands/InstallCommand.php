<?php

namespace App\Console\Commands;

use Database\Seeders\DatabaseSeeder;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;

class InstallCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'install';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '安装程序';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(Filesystem $filesystem)
    {
        $lockFile = base_path('install.lock');
        if ($filesystem->exists($lockFile)) {
            $this->output->error('程序已经安装，请勿重新安装');
            return Command::FAILURE;
        }

        if (!$filesystem->exists($envFile = base_path('.env'))) {
            $this->output->info('拷贝.env文件');
            copy(base_path('.env.example'), $envFile);
        }

        if ($this->ask('是否要重建所有表结构？')) {
            $this->call('migrate:fresh');
        } else {
            $this->call('migrate');
        }

        $this->call('passport:install');
        $this->call('passport:keys', ['--force' => true]);

        if ($this->ask('确定要执行种子文件吗？')) {
            $this->call('db:seed');
        }

        if ($this->ask('确定要重新生成APP KEY吗？')) {
            $this->call('key:generate');
        }

        $filesystem->put($lockFile, time());

        return Command::SUCCESS;
    }
}
