<?php

namespace App\Services;

use GuzzleHttp\RequestOptions;

class MoonshotService
{
    public function chat($answer)
    {
        $client   = new \GuzzleHttp\Client();
        $response = $client->post('https://api.moonshot.cn/v1/chat/completions', [
            RequestOptions::HEADERS => [
                'Authorization' => 'Bearer sk-tJ7mJv9POmflg9DlDPJyWKn3DULtvvwMcr1MbmufYPOBfGcm',
                'Content-Type'  => 'application/json',
            ],
            RequestOptions::STREAM  => true,
            RequestOptions::JSON    => [
                'model'       => 'moonshot-v1-8k',
                'messages'    => [
                    [
                        'role'    => 'user',
                        'content' => $answer,
                    ]
                ],
                'temperature' => 0.3,
                'stream'      => true,
            ],
        ]);
        $stream   = $response->getBody();
        $buffer   = '';
        while (!$stream->eof()) {
            $buffer   .= $stream->read(24);
            $position = strpos($buffer, "\n\n");
            if ($position === false) {
                continue;
            }
            $firstPart = substr($buffer, 0, $position + 2);
            $buffer    = substr($buffer, $position + 2);
            echo $firstPart;
            if (connection_aborted()) {
                break;
            }
        }
    }
}
