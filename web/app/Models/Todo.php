<?php

namespace App\Models;

use App\Enum\TodoStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\SoftDeletes;

class Todo extends Model
{
    use SoftDeletes;
    use HasUuids;

    protected $fillable = ['user_id', 'title', 'description', 'status', 'deadline', 'completed_at'];
    protected $casts    = [
        'status'       => TodoStatus::class,
        'completed_at' => 'datetime',
        'deadline' => 'datetime',
    ];
    protected $hidden   = ['id', 'user_id', self::UPDATED_AT, self::DELETED_AT];

    public function uniqueIds()
    {
        return ['uuid'];
    }
}
