<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        'Illuminate\Auth\Events\Registered' => [
            'Illuminate\Auth\Listeners\SendEmailVerificationNotification',
        ],
        'Illuminate\Database\Events\QueryExecuted' => [
            'App\Listeners\DatabaseQueryListener',
        ],
        'Laravel\Passport\Events\AccessTokenCreated' => [
            'App\Listeners\LogUserClientGrant',
        ],
        'Illuminate\Auth\Events\PasswordReset' => [
            'App\Listeners\RevokeTokens',
        ],
        'Illuminate\Auth\Events\Logout' => [
            'App\Listeners\RevokeTokens',
        ]
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
