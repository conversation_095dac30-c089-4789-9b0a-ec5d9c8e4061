<?php

namespace App\Http\Resources;

use App\Models\Client;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ClientGrantedUsersCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->transform(function (User $user) use ($request) {
                return [
                    'id' => $user->getKey(),
                    'nickname' => $user->nickname,
                    'name' => $user->name,
                    'avatar' => $user->avatar,
                    'authorized_at' => $user->pivot->updated_at,
                ];
            }),
            'total' => $this->total(),
            'current_page' => $this->currentPage(),
            'per_page' => $this->perPage(),
        ];
    }
}
