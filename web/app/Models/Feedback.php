<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property Carbon $solved_at
 * @property User   $user
 */
class Feedback extends Model
{
    use SoftDeletes, HasUuids;

    protected $table    = 'feedbacks';
    protected $fillable = ['feedback', 'user_id', 'solved_at'];
    protected $casts    = ['solved_at' => 'datetime'];

    public function uniqueIds()
    {
        return ['uuid'];
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function markedAsSolved($solvedAt = null): void
    {
        $this->update(['solved_at' => $solvedAt ?: now()]);
    }
}
